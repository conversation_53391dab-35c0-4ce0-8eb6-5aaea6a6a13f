import { But<PERSON> } from "@ui/components/button";
import { ReactNode } from "react";

interface AdminPageLayoutProps {
	title: string;
	subtitle: string;
	actionButton?: {
		label: string;
		onClick: () => void;
		icon?: ReactNode;
	};
	children: ReactNode;
}

export function AdminPageLayout({
	title,
	subtitle,
	actionButton,
	children,
}: AdminPageLayoutProps) {
	return (
		<div className="space-y-6">
			<div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:justify-between">
				<div>
					<h2 className="text-2xl font-semibold">{title}</h2>
					<p className="text-muted-foreground">{subtitle}</p>
				</div>

				{actionButton && (
					<Button onClick={actionButton.onClick}>
						{actionButton.icon}
						{actionButton.label}
					</Button>
				)}
			</div>
			{children}
		</div>
	);
}
