'use client'

import { Card, CardContent } from '@/modules/ui/components/card'
import { Skeleton } from '@/modules/ui/components/skeleton'

export function CourseCardSkeleton() {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-all duration-200 group h-full">
      <CardContent className="p-0">
        <div className="flex h-full min-h-[300px]">
          
          <div className="relative w-32 lg:w-56 flex-shrink-0">
            <Skeleton className="w-full h-full rounded-l-lg" />
          </div>

   
          <div className="flex-1 p-4 flex flex-col justify-between min-w-0">
            <div className="space-y-2">
       
              <div className="flex justify-between items-start">
                <Skeleton className="h-3 w-20" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>

    
              <div className="space-y-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>

    
              <Skeleton className="h-3 w-24" />

    
              <div className="flex items-center gap-3">
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-3 w-20" />
              </div>
            </div>

     
            <div className="flex items-center justify-end pt-2 border-t mt-3 border-border">
              <div className="flex gap-1">
                <Skeleton className="h-7 w-12" />
                <Skeleton className="h-7 w-14" />
                <Skeleton className="h-7 w-8" />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function CourseGridSkeleton({ count = 6 }: { count?: number }) {
  return (
    <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
      {Array.from({ length: count }).map((_, index) => (
        <CourseCardSkeleton key={index} />
      ))}
    </div>
  )
}