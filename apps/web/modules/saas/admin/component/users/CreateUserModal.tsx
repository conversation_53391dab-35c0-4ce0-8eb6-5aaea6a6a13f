"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { toast } from "sonner";

interface CreateUserModalProps {
	isOpen: boolean;
	onClose: () => void;
	onUserCreated?: () => void;
}

export function CreateUserModal({
	isOpen,
	onClose,
	onUserCreated,
}: CreateUserModalProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [formData, setFormData] = useState({
		name: "",
		email: "",
		role: "user",
		sendEmail: true,
	});

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsLoading(true);

		try {
			const response = await fetch("/api/admin/users", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(formData),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "Erro ao criar usuário");
			}

			if (formData.sendEmail) {
				toast.success(
					`Usuário criado com sucesso! Email enviado para ${formData.email}`,
					{
						duration: 5000,
					}
				);
			} else {
				toast.success("Usuário criado com sucesso!");
			}

			onUserCreated?.();
			onClose();

			// Reset form
			setFormData({
				name: "",
				email: "",
				role: "user",
				sendEmail: true,
			});
		} catch (error: any) {
			toast.error(error.message || "Erro ao criar usuário");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Criar Novo Membro</DialogTitle>
					<DialogDescription>
						Adicione um novo membro à plataforma. O usuário receberá um
						email com suas credenciais de acesso.
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="name">Nome</Label>
						<Input
							id="name"
							value={formData.name}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									name: e.target.value,
								}))
							}
							placeholder="Nome completo"
							required
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="email">Email</Label>
						<Input
							id="email"
							type="email"
							value={formData.email}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									email: e.target.value,
								}))
							}
							placeholder="<EMAIL>"
							required
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="role">Função</Label>
						<Select
							value={formData.role}
							onValueChange={(value) =>
								setFormData((prev) => ({ ...prev, role: value }))
							}
						>
							<SelectTrigger>
								<SelectValue placeholder="Selecione uma função" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="user">Membro</SelectItem>
								<SelectItem value="admin">Administrador</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div className="flex items-center space-x-2">
						<input
							id="sendEmail"
							type="checkbox"
							checked={formData.sendEmail}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									sendEmail: e.target.checked,
								}))
							}
							className="rounded border-gray-300"
						/>
						<Label htmlFor="sendEmail" className="text-sm">
							Enviar email com credenciais de acesso
						</Label>
					</div>

					<div className="flex justify-end gap-2 pt-4">
						<Button type="button" variant="outline" onClick={onClose}>
							Cancelar
						</Button>
						<Button type="submit" disabled={isLoading}>
							{isLoading ? "Criando..." : "Criar Membro"}
						</Button>
					</div>
				</form>
			</DialogContent>
		</Dialog>
	);
}
