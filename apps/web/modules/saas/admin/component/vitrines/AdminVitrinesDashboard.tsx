'use client'

import { useState, useMemo, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from '@/modules/ui/components/card'
import { Button } from '@/modules/ui/components/button'
import { Badge } from '@/modules/ui/components/badge'
import {
  PlusIcon,
  PlayIcon,
  EyeIcon,
  EditIcon,
  TrashIcon,
  UsersIcon
} from 'lucide-react'
import { toast } from 'sonner'
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/modules/ui/components/select'

interface Organization {
  id: string
  name: string
  slug: string
  vitrinesCount: number
}

interface Vitrine {
  id: string
  name: string
  organization: string
  organizationSlug: string
  views: number
  status: 'active' | 'inactive' | 'draft'
  type: 'free' | 'paid'
  price?: number
  createdAt: string
}

// Dados de organizações - implementação real será feita posteriormente
const mockOrganizations: Organization[] = [
  { id: '1', name: 'Cakto Academy', slug: 'cakto-academy', vitrinesCount: 5 },
  { id: '2', name: 'Tech Corp', slug: 'tech-corp', vitrinesCount: 3 },
  { id: '3', name: 'Marketing Hub', slug: 'marketing-hub', vitrinesCount: 2 }
]

// Dados de vitrines - implementação real será feita posteriormente
const mockVitrines: Vitrine[] = [
  {
    id: '1',
    name: 'Vitrine Principal',
    organization: 'Cakto Academy',
    organizationSlug: 'cakto-academy',
    views: 1245,
    status: 'active',
    type: 'free',
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    name: 'Vitrine de Produtos',
    organization: 'Tech Corp',
    organizationSlug: 'tech-corp',
    views: 856,
    status: 'active',
    type: 'paid',
    price: 99.90,
    createdAt: '2024-01-10'
  },
  {
    id: '3',
    name: 'Vitrine de Serviços',
    organization: 'Marketing Hub',
    organizationSlug: 'marketing-hub',
    views: 432,
    status: 'draft',
    type: 'free',
    createdAt: '2024-01-20'
  }
]

export function AdminVitrinesDashboard() {
  const router = useRouter()
  const [selectedOrg, setSelectedOrg] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')

  const filteredVitrines = useMemo(() => {
    return mockVitrines.filter(vitrine => {
      const matchesOrg = selectedOrg === 'all' || vitrine.organizationSlug === selectedOrg
      const matchesStatus = statusFilter === 'all' || vitrine.status === statusFilter

      return matchesOrg && matchesStatus
    })
  }, [selectedOrg, statusFilter])

  const handleCreateVitrine = useCallback(() => {
    if (mockOrganizations.length === 1) {
      const organization = mockOrganizations[0]
      router.push(`/app/${organization.slug}/vitrines/create`)
      return
    }
    if (selectedOrg === 'all') {
      toast.error('Selecione uma organização para criar uma vitrine')
      return
    }

    const organization = mockOrganizations.find(org => org.slug === selectedOrg)
    if (organization) {
      router.push(`/app/${organization.slug}/vitrines/create`)
    }
  }, [selectedOrg, router])

  const handleViewVitrine = useCallback((vitrine: Vitrine) => {
    router.push(`/app/${vitrine.organizationSlug}/vitrine/${vitrine.id}`)
  }, [router])

  const handleEditVitrine = useCallback((vitrine: Vitrine) => {
    router.push(`/app/${vitrine.organizationSlug}/vitrine/${vitrine.id}/edit`)
  }, [router])

  const handleDeleteVitrine = useCallback((vitrine: Vitrine) => {

  }, [])


  const shouldShowOrgSelector = mockOrganizations.length > 1

  return (
    <div className="space-y-6">
      {/* Filtros */}
      {(shouldShowOrgSelector || statusFilter !== 'all') && (
        <div className="flex gap-4 mb-6">
          {shouldShowOrgSelector && (
            <Select value={selectedOrg} onValueChange={setSelectedOrg}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Workspaces" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Workspaces</SelectItem>
                {mockOrganizations.map(org => (
                  <SelectItem key={org.id} value={org.slug}>{org.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Todos os status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os status</SelectItem>
              <SelectItem value="active">Ativo</SelectItem>
              <SelectItem value="draft">Rascunho</SelectItem>
              <SelectItem value="inactive">Inativo</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={() => {
            setSelectedOrg('all')
            setStatusFilter('all')
          }}>
            Limpar Filtros
          </Button>
        </div>
      )}

      {/* Lista de vitrines em cards */}
      {filteredVitrines.length === 0 ? (
        <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <PlayIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma vitrine encontrada</h3>
              <p className="text-gray-600 mb-4">
                {selectedOrg !== 'all' || statusFilter !== 'all'
                  ? 'Tente ajustar os filtros de busca'
                  : 'Comece criando sua primeira vitrine'
                }
              </p>
              {selectedOrg !== 'all' && (
                <Button onClick={handleCreateVitrine}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Criar Primeira Vitrine
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredVitrines.map(vitrine => (
            <Card key={vitrine.id} className="p-6 hover:shadow-md transition-shadow bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="size-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <PlayIcon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{vitrine.name}</h3>
                      <div className="flex items-center gap-1">
                        <Badge className={
                          vitrine.status === 'active' ? 'bg-green-100 text-green-800' :
                          vitrine.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }>
                          {vitrine.status === 'active' ? 'Ativo' :
                           vitrine.status === 'draft' ? 'Rascunho' : 'Inativo'}
                        </Badge>
                        <Badge className={vitrine.type === 'free' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}>
                          {vitrine.type === 'free' ? 'Gratuito' : `R$ ${vitrine.price?.toFixed(2)}`}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground">
                  {vitrine.organization}
                </div>

                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <EyeIcon className="h-3 w-3" />
                    {vitrine.views} visualizações
                  </span>
                </div>

                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="primary"
                    className="flex-1"
                    onClick={() => handleViewVitrine(vitrine)}
                  >
                    Acessar
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleEditVitrine(vitrine)}>
                    <EditIcon className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700" onClick={() => handleDeleteVitrine(vitrine)}>
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
