'use client'

import { Lesson } from '../../types'
import { Card, CardContent } from '@/modules/ui/components/card'
import { Button } from '@/modules/ui/components/button'
import { Badge } from '@/modules/ui/components/badge'
import { Switch } from '@/modules/ui/components/switch'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@ui/components/dropdown-menu'
import { GripVertical, Play, MoreVertical, Edit, Trash2, FileText, Clock } from 'lucide-react'
import { cn } from '@/modules/ui/lib'

interface LessonCardProps {
  lesson: Lesson
  onEdit: () => void
  onDelete: () => void
}

export function LessonCard({ lesson, onEdit, onDelete }: LessonCardProps) {
  const hasVideo = !!lesson.videoUrl

  return (
    <Card className="group hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          {/* Drag Handle */}
          <div className="cursor-grab text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity">
            <GripVertical className="h-4 w-4" />
          </div>

          {/* Video Icon */}
          <div className={cn(
            "flex items-center justify-center w-8 h-8 rounded",
            hasVideo ? "bg-[#36B37E] text-white" : "bg-gray-100 text-gray-400"
          )}>
            <Play className="h-4 w-4" />
          </div>

          {/* Lesson Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-gray-900 truncate">{lesson.name}</h4>
            </div>

            {/* Lesson Meta */}
            <div className="flex items-center gap-4 text-xs text-gray-500">
              {lesson.duration && (
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {lesson.duration}
                </div>
              )}
            </div>

            {lesson.description && (
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {lesson.description}
              </p>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">

            {/* More Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onDelete} className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
