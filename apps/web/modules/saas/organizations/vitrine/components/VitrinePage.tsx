"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { ShowcaseItem } from "../types";
import { Skeleton } from "@ui/components/skeleton";
import { HeroBannerFrontend } from "./HeroBannerFrontend";
import { NetflixCarousel } from "./NetflixCarousel";
import { CoursePreviewModal } from "./CoursePreviewModal";
import { useVitrineSettings } from "../hooks/useVitrineSettings";
import { useOrganizationVitrine } from "../hooks/useVitrines";
import { useMockCoursePreview } from "../hooks/useCoursePreview";
import { cn } from "@ui/lib";

interface VitrinePageProps {
	organizationSlug: string;
}

interface VitrineData {
	title: string;
	description: string;
	bannerImage: string;
	sections: Array<{
		id: string;
		title: string;
		subtitle?: string;
		description?: string;
		isLocked: boolean;
		accessType: string;
		checkoutUrl?: string;
		courses: Array<{
			id: string;
			name: string;
			logo?: string;
			community?: string;
		}>;
	}>;
}

export function VitrinePage({ organizationSlug }: VitrinePageProps) {
	const router = useRouter();
	const [purchaseLoading, setPurchaseLoading] = useState<string | null>(null);
	const [userAccess, setUserAccess] = useState<Record<string, boolean>>({});
	const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
	const [selectedCourse, setSelectedCourse] = useState<any>(null);
	const [previewLoading, setPreviewLoading] = useState(false);
	const [previewError, setPreviewError] = useState<string | null>(null);
	const { settings, loading: loadingSettings } = useVitrineSettings();


	const {
		data: vitrineResponse,
		isLoading: isLoadingData,
		error,
	} = useOrganizationVitrine(organizationSlug);


	const vitrineData = vitrineResponse?.data || null;


	useEffect(() => {
		if (vitrineData?.sections) {
			const allCourseIds = vitrineData.sections.flatMap((section: VitrineData['sections'][0]) =>
				(section.courses || []).map((course: any) => course.id),
			);


			const mockAccess: Record<string, boolean> = {};
			vitrineData.sections.forEach((section: VitrineData['sections'][0]) => {
				(section.courses || []).forEach((course: any) => {
					mockAccess[course.id] =
						!section.isLocked || section.accessType === "FREE";
				});
			});
			setUserAccess(mockAccess);
		}
	}, [vitrineData]);

	const handlePlay = (item: ShowcaseItem) => {

		if (item.sectionLocked && !userAccess[item.id]) {
			toast.error(
				"Você precisa adquirir acesso a esta seção para assistir este curso",
			);
			return;
		}

		if (item.isContinueWatching) {
			router.push(`/app/${organizationSlug}/course/${item.id}`);
			return;
		}

		router.push(`/app/${organizationSlug}/course/${item.id}`);
	};

	const handlePurchase = async (item: ShowcaseItem) => {
		if (!item.sectionLocked || !item.sectionId) {
			toast.error("Este item não está disponível para compra");
			return;
		}


		const section = vitrineData?.sections.find(
			(s: VitrineData['sections'][0]) => s.id === item.sectionId,
		);
		if (!section?.checkoutUrl) {
			toast.error("Link de checkout não configurado");
			return;
		}


		window.open(section.checkoutUrl, "_blank");
	};

	const handleHeroPlay = () => {
		if (vitrineData?.sections?.[0]?.courses?.[0]) {
			const firstCourse = vitrineData.sections[0].courses[0];
			const showcaseItem: ShowcaseItem = {
				id: firstCourse.id,
				title: firstCourse.name,
				image: firstCourse.logo || "/images/cards/card1.jpg",
				description:
					firstCourse.community || "Curso disponível na plataforma",
				category: "Curso",
				progress: 0,
				duration: "2h 30min",
				studentsCount: 100,
				isLocked: vitrineData.sections[0].isLocked,
				sectionLocked: vitrineData.sections[0].isLocked,
				sectionId: vitrineData.sections[0].id,
				isContinueWatching: false,
			};
			handlePlay(showcaseItem);
		}
	};

		const handleHeroInfo = async () => {
		if (vitrineData?.sections?.[0]?.courses?.[0]) {
			const firstCourse = vitrineData.sections[0].courses[0];

			setPreviewLoading(true);
			setPreviewError(null);

			try {
				// Buscar dados reais da API
				const response = await fetch(`/api/courses/${firstCourse.id}/preview?organizationSlug=${organizationSlug}`);

				if (response.ok) {
					const courseData = await response.json();
					setSelectedCourse(courseData);
					setIsPreviewModalOpen(true);
				} else {

					const { courseData } = useMockCoursePreview(firstCourse.id);
					setSelectedCourse(courseData);
					setIsPreviewModalOpen(true);
				}
			} catch (error) {
				console.error('Erro ao buscar dados do curso:', error);
				setPreviewError('Erro ao carregar preview do curso');

				const { courseData } = useMockCoursePreview(firstCourse.id);
				setSelectedCourse(courseData);
				setIsPreviewModalOpen(true);
			} finally {
				setPreviewLoading(false);
			}
		}
	};


	if (isLoadingData || loadingSettings) {
		return (
			<div className="min-h-screen bg-background">
				<div className="space-y-8">
					{/* Hero Banner Skeleton */}
					<div className="relative min-h-[60vh] bg-muted animate-pulse" />

					{/* Sections Skeleton */}
					{Array.from({ length: 3 }).map((_, index) => (
						<div
							key={index}
							className="container mx-auto px-4 space-y-4"
						>
							<Skeleton className="h-8 w-48" />
							<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
								{Array.from({ length: 4 }).map(
									(_, cardIndex) => (
										<Skeleton
											key={cardIndex}
											className="aspect-[16/9] rounded-lg"
										/>
									),
								)}
							</div>
						</div>
					))}
				</div>
			</div>
		);
	}


	if (error) {
		return (
			<div className="min-h-screen bg-background">
				<div className="flex items-center justify-center min-h-[50vh]">
					<div className="text-center space-y-4">
						<h1 className="text-2xl font-bold text-foreground">
							Erro ao carregar vitrine
						</h1>
						<p className="text-muted-foreground">{error instanceof Error ? error.message : "Erro ao carregar dados da vitrine"}</p>
					</div>
				</div>
			</div>
		);
	}


	if (!vitrineData) {
		return (
			<div className="min-h-screen bg-background">
				<div className="flex items-center justify-center min-h-[50vh]">
					<div className="text-center space-y-4">
						<h1 className="text-2xl font-bold text-foreground">
							Nenhuma vitrine encontrada
						</h1>
						<p className="text-muted-foreground">
							Esta organização não possui vitrines configuradas.
						</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen">
			{/* Novo HeroBanner para teste visual */}
			{vitrineData && (
				<HeroBannerFrontend
					title={vitrineData.title}
					subtitle="Cursos e treinamentos"
					description={vitrineData.description || "Descubra nossos cursos e treinamentos exclusivos"}
					backgroundImage={
						vitrineData.bannerImage || "/images/banner1.jpg"
					}
					rating={4.8}
					duration="50h+ de conteúdo"
					studentsCount={
						vitrineData.sections.reduce(
							(acc: number, section: VitrineData['sections'][0]) => acc + section.courses.length,
							0,
						) * 100
					}
					onPlay={handleHeroPlay}
					onInfo={handleHeroInfo}
					infoLoading={previewLoading}
				/>
			)}

			{/* Sections */}
			<div className="container mx-auto px-4 py-16 space-y-16">
				{vitrineData.sections.map((section: VitrineData['sections'][0], index: number) => {
					const showcaseItems: ShowcaseItem[] = section.courses.map(
						(course: VitrineData['sections'][0]['courses'][0]) => ({
							id: course.id,
							title: course.name,
							image: course.logo || "/images/cards/card1.jpg",
							description:
								course.community ||
								"Curso disponível na plataforma",
							category: section.title,
							progress: 0,
							duration: "2h 30min",
							studentsCount: 100,
							isLocked: section.isLocked,
							sectionLocked: section.isLocked,
							sectionId: section.id,
							isContinueWatching: false,
						}),
					);

					return (
						<motion.div
							key={section.id}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: index * 0.1 }}
						>
							<NetflixCarousel
								title={section.title}
								subtitle={section.subtitle ?? undefined}
								items={showcaseItems}
								onPlay={handlePlay}
								onPurchase={handlePurchase}
							/>
						</motion.div>
					);
				})}
			</div>

			{/* Purchase Loading Overlay */}
			{purchaseLoading && (
				<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
					<div className="bg-background p-8 rounded-lg text-center border shadow-lg">
						<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
						<p className="text-foreground text-lg">
							Carregando checkout...
						</p>
						<p className="text-muted-foreground text-sm mt-2">
							Você será redirecionado para a página de pagamento
						</p>
					</div>
				</div>
			)}

			{/* Course Preview Modal */}
			{selectedCourse && (
				<CoursePreviewModal
					isOpen={isPreviewModalOpen}
					onClose={() => {
						setIsPreviewModalOpen(false);
						setSelectedCourse(null);
					}}
					course={selectedCourse}
					onPlayLesson={(lessonId) => {


						// Fechar modal e redirecionar para a aula
						setIsPreviewModalOpen(false);
						router.push(`/app/${organizationSlug}/course/${selectedCourse.id}?lesson=${lessonId}`);
					}}
				/>
			)}
		</div>
	);
}
