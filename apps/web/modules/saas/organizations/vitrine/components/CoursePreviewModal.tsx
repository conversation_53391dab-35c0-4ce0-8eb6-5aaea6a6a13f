"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@ui/components/button";
import { X, Play, Check, ChevronDown } from "lucide-react";
import { CoursePreviewModalProps, Lesson } from "../types/course-preview";

export const CoursePreviewModal = React.memo(function CoursePreviewModal({
  isOpen,
  onClose,
  course,
  onPlayLesson,
}: CoursePreviewModalProps) {
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);

  useEffect(() => {
    if (course.firstLesson) {
      setSelectedLesson(course.firstLesson);
    } else if (course.modules.length > 0 && course.modules[0].lessons.length > 0) {
      setSelectedLesson(course.modules[0].lessons[0]);
    }
  }, [course]);

  const handlePlayLesson = useCallback((lesson: Lesson) => {
    setSelectedLesson(lesson);
    onPlayLesson?.(lesson.id);
  }, [onPlayLesson]);

  // Calculate total lessons count
  const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);

  // Get background image from course data
  const backgroundImage = course.image || course.vitrineImage || course.backgroundImage || course.logo;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="relative w-full max-w-2xl bg-black rounded-xl overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <div className="absolute top-4 right-4 z-20">
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0 text-white/80 hover:text-white hover:bg-black/30 rounded-full"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Hero Section with Background */}
            <div className="relative h-64 overflow-hidden">
              {backgroundImage ? (
                <div
                  className="absolute inset-0 bg-cover bg-center"
                  style={{ backgroundImage: `url(${backgroundImage})` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent" />
                </div>
              ) : (
                <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-slate-900">
                  <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent" />
                </div>
              )}

              {/* Course Info Overlay */}
              <div className="absolute bottom-0 left-0 right-0 p-6">
                <p className="text-white/80 text-sm mb-1">Comece Aqui</p>
                <h1 className="text-2xl font-bold text-white mb-3">{course.title}</h1>

                {/* Play Button */}
                {selectedLesson && (
                  <Button
                    onClick={() => handlePlayLesson(selectedLesson)}
                    className="bg-white text-black hover:bg-gray-200 px-4 py-2 rounded-md font-medium flex items-center gap-2"
                  >
                    <Play className="h-4 w-4 fill-black" />
                    Play
                  </Button>
                )}
              </div>
            </div>

            {/* Content Section */}
            <div className="p-6">
              {/* Course Stats */}
              <div className="mb-4">
                <h2 className="text-xl font-bold text-white mb-2">{course.title}</h2>
                <p className="text-gray-400 text-sm">
                  {course.modules.length} módulo{course.modules.length > 1 ? 's' : ''} • {totalLessons} aula{totalLessons > 1 ? 's' : ''}
                </p>
              </div>

              {/* Module Dropdown */}
              {course.modules.length > 0 && (
                <div className="mb-4">
                  <div className="bg-gray-800/50 rounded-lg border border-gray-700">
                    <div className="flex items-center justify-between p-3 cursor-pointer">
                      <span className="text-white font-medium text-sm">{course.modules[0].title}</span>
                      <ChevronDown className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                </div>
              )}

              {/* First Lesson Preview */}
              {course.modules.length > 0 && course.modules[0].lessons.length > 0 && (
                <div
                  className="bg-gray-900/50 rounded-lg p-3 border border-gray-700 cursor-pointer hover:bg-gray-900/70 transition-colors"
                  onClick={() => handlePlayLesson(course.modules[0].lessons[0])}
                >
                  <div className="flex items-center gap-3">
                    {/* Completion Checkbox */}
                    <div className="w-4 h-4 rounded border border-gray-500 flex items-center justify-center flex-shrink-0">
                      {course.modules[0].lessons[0].isCompleted && (
                        <Check className="h-2.5 w-2.5 text-green-400" />
                      )}
                    </div>

                    {/* Lesson Thumbnail */}
                    <div className="w-16 h-10 bg-gray-800 rounded overflow-hidden flex-shrink-0">
                      {course.modules[0].lessons[0].thumbnail ? (
                        <img
                          src={course.modules[0].lessons[0].thumbnail}
                          alt={course.modules[0].lessons[0].title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <div className="text-blue-400 font-bold text-xs">acquisition360</div>
                        </div>
                      )}
                    </div>

                    {/* Lesson Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-white font-medium text-sm">
                        {course.modules[0].lessons[0].title}
                      </h3>
                    </div>

                    {/* Duration */}
                    <div className="text-gray-400 text-xs flex-shrink-0">
                      {course.modules[0].lessons[0].duration}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
});
