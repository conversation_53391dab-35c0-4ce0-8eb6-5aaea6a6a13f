"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@ui/components/button";
import { X, Play, Check, Clock } from "lucide-react";
import { CoursePreviewModalProps, Lesson } from "../types/course-preview";

export const CoursePreviewModal = React.memo(function CoursePreviewModal({
  isOpen,
  onClose,
  course,
  onPlayLesson,
}: CoursePreviewModalProps) {
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);

  useEffect(() => {
    if (course.firstLesson) {
      setSelectedLesson(course.firstLesson);
    } else if (course.modules.length > 0 && course.modules[0].lessons.length > 0) {
      setSelectedLesson(course.modules[0].lessons[0]);
    }
  }, [course]);

  const handlePlayLesson = useCallback((lesson: Lesson) => {
    setSelectedLesson(lesson);
    onPlayLesson?.(lesson.id);
  }, [onPlayLesson]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="relative w-full max-w-2xl bg-slate-800 rounded-2xl overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <div className="absolute top-4 right-4 z-20">
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700 rounded-full"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="p-8">
              {/* Course Title */}
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">{course.title}</h2>
                {course.description && (
                  <p className="text-gray-400 text-sm">{course.description}</p>
                )}
              </div>

              {/* Selected Lesson Preview */}
              {selectedLesson && (
                <div className="mb-6">
                  <div className="flex items-center gap-4 p-4 bg-slate-700 rounded-lg">
                    {selectedLesson.thumbnail ? (
                      <img
                        src={selectedLesson.thumbnail}
                        alt={selectedLesson.title}
                        className="w-16 h-10 object-cover rounded"
                      />
                    ) : (
                      <div className="w-16 h-10 bg-slate-600 rounded flex items-center justify-center">
                        <Play className="h-5 w-5 text-gray-400" />
                      </div>
                    )}
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-white">{selectedLesson.title}</h3>
                      <div className="flex items-center gap-2 text-sm text-gray-400">
                        <Clock className="h-3 w-3" />
                        <span>{selectedLesson.duration}</span>
                      </div>
                    </div>
                    <Button
                      onClick={() => handlePlayLesson(selectedLesson)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                    >
                      <Play className="h-4 w-4" />
                      Assistir
                    </Button>
                  </div>
                </div>
              )}

              {/* Course Modules */}
              <div className="space-y-4">
                {course.modules.map((module) => (
                  <div key={module.id}>
                    <div className="mb-3">
                      <h4 className="text-lg font-semibold text-white mb-1">{module.title}</h4>
                      <p className="text-sm text-gray-400">
                        {module.lessons.length} aula{module.lessons.length > 1 ? 's' : ''}
                      </p>
                    </div>

                    <div className="space-y-2">
                      {module.lessons.map((lesson) => (
                        <div
                          key={lesson.id}
                          className={`flex items-center gap-3 p-3 rounded-lg hover:bg-slate-700 transition-colors cursor-pointer ${
                            selectedLesson?.id === lesson.id ? 'bg-slate-700' : 'bg-slate-600'
                          }`}
                          onClick={() => handlePlayLesson(lesson)}
                        >
                          {/* Completion Status */}
                          <div className="w-4 h-4 rounded border border-gray-400 flex items-center justify-center flex-shrink-0">
                            {lesson.isCompleted && (
                              <Check className="h-3 w-3 text-green-400" />
                            )}
                          </div>

                          {/* Lesson Thumbnail */}
                          <div className="w-10 h-6 bg-slate-500 rounded flex-shrink-0 overflow-hidden">
                            {lesson.thumbnail ? (
                              <img
                                src={lesson.thumbnail}
                                alt={lesson.title}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Play className="h-3 w-3 text-gray-300" />
                              </div>
                            )}
                          </div>

                          {/* Lesson Info */}
                          <div className="flex-1 min-w-0">
                            <h5 className="text-sm font-medium text-white truncate">
                              {lesson.title}
                            </h5>
                          </div>

                          {/* Duration */}
                          <div className="text-xs text-gray-400 flex-shrink-0">
                            {lesson.duration}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
});
