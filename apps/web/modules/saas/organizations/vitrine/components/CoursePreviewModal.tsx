"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@ui/components/button";
import { X, Play, Check, Clock } from "lucide-react";
import { CoursePreviewModalProps, Lesson } from "../types/course-preview";

export const CoursePreviewModal = React.memo(function CoursePreviewModal({
  isOpen,
  onClose,
  course,
  onPlayLesson,
}: CoursePreviewModalProps) {
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);

  useEffect(() => {
    if (course.firstLesson) {
      setSelectedLesson(course.firstLesson);
    } else if (course.modules.length > 0 && course.modules[0].lessons.length > 0) {
      setSelectedLesson(course.modules[0].lessons[0]);
    }
  }, [course]);

  const handlePlayLesson = useCallback((lesson: Lesson) => {
    setSelectedLesson(lesson);
    onPlayLesson?.(lesson.id);
  }, [onPlayLesson]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="relative w-full max-w-2xl bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 rounded-2xl overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Background Banner Effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-slate-900/40"></div>

            {/* Close Button */}
            <div className="absolute top-4 right-4 z-20">
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-black/30 rounded-full"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="relative z-10 p-8">
              {/* Course Title */}
              <div className="text-center mb-8">
                <p className="text-gray-400 text-sm mb-2">{course.description || course.title}</p>
                <h2 className="text-3xl font-bold text-white mb-6">{course.title}</h2>

                {/* Assistir Button */}
                {selectedLesson && (
                  <Button
                    onClick={() => handlePlayLesson(selectedLesson)}
                    className="bg-white text-black hover:bg-gray-200 px-8 py-3 rounded-lg font-semibold flex items-center gap-2 mx-auto"
                  >
                    <Play className="h-5 w-5 fill-black" />
                    Assistir
                  </Button>
                )}
              </div>

              {/* Course Modules */}
              <div className="space-y-6">
                {course.modules.map((module) => (
                  <div key={module.id}>
                    <div className="mb-4">
                      <h4 className="text-xl font-semibold text-white mb-1">{module.title}</h4>
                      <p className="text-sm text-gray-400">
                        {module.lessons.length} módulo • {module.lessons.length} aula{module.lessons.length > 1 ? 's' : ''}
                      </p>
                    </div>

                    {/* Module Dropdown */}
                    <div className="mb-4">
                      <div className="bg-black/30 rounded-lg p-3 border border-gray-600">
                        <div className="flex items-center justify-between text-white">
                          <span className="font-medium">{module.title}</span>
                          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    </div>

                    {/* Lesson Preview */}
                    {module.lessons.map((lesson, index) => (
                      index === 0 && (
                        <div
                          key={lesson.id}
                          className="bg-black/40 rounded-lg p-4 border border-gray-600 cursor-pointer hover:bg-black/50 transition-colors"
                          onClick={() => handlePlayLesson(lesson)}
                        >
                          <div className="flex items-center gap-4">
                            {/* Completion Status */}
                            <div className="w-5 h-5 rounded border border-gray-400 flex items-center justify-center flex-shrink-0">
                              {lesson.isCompleted && (
                                <Check className="h-3 w-3 text-green-400" />
                              )}
                            </div>

                            {/* Lesson Thumbnail */}
                            <div className="w-20 h-12 bg-black/50 rounded overflow-hidden flex-shrink-0">
                              {lesson.thumbnail ? (
                                <img
                                  src={lesson.thumbnail}
                                  alt={lesson.title}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <div className="text-blue-400 font-bold text-xs">acquisition360</div>
                                </div>
                              )}
                            </div>

                            {/* Lesson Info */}
                            <div className="flex-1 min-w-0">
                              <h5 className="text-base font-medium text-white mb-1">
                                {lesson.title}
                              </h5>
                            </div>

                            {/* Duration */}
                            <div className="text-sm text-gray-400 flex-shrink-0">
                              {lesson.duration}
                            </div>
                          </div>
                        </div>
                      )
                    ))}
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
});
