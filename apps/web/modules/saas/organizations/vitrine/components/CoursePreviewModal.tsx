"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@ui/components/button";
import { X, Play, Check, Clock, ChevronDown } from "lucide-react";
import { CoursePreviewModalProps, Lesson } from "../types/course-preview";

export const CoursePreviewModal = React.memo(function CoursePreviewModal({
  isOpen,
  onClose,
  course,
  onPlayLesson,
}: CoursePreviewModalProps) {
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string>("");
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [selectedModule, setSelectedModule] = useState<string>("");

 
  useEffect(() => {
    if (course.firstLesson?.videoUrl) {
      setCurrentVideoUrl(course.firstLesson.videoUrl);
    }
    if (course.modules.length > 0) {
      setSelectedModule(course.modules[0].id);
    }
  }, [course.firstLesson, course.modules]);

  const handlePlayLesson = useCallback((lesson: any) => {
    if (lesson.videoUrl) {
      setCurrentVideoUrl(lesson.videoUrl);
      setIsVideoPlaying(true);
    }
    onPlayLesson?.(lesson.id);
  }, [onPlayLesson]);

  const handleResume = useCallback(() => {
    setIsVideoPlaying(true);
  }, []);

  // Memoizar cálculos
  const { totalLessons, completedLessons } = useMemo(() => {
    const total = course.modules.reduce((acc, module) => acc + module.lessons.length, 0);
    const completed = course.modules.reduce(
      (acc, module) => acc + module.lessons.filter(lesson => lesson.isCompleted).length,
      0
    );
    return { totalLessons: total, completedLessons: completed };
  }, [course.modules]);

  const currentModule = course.modules.find(m => m.id === selectedModule);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="relative w-full max-w-4xl max-h-[90vh] bg-gray-900 rounded-xl overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <div className="absolute top-4 right-4 z-20">
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0 bg-black/50 hover:bg-black/70 text-white rounded-full"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex flex-col h-full">
   
              <div className="flex-1 p-6">
               
 

                {/* Video Player */}
                <div className="relative aspect-video bg-black rounded-lg mb-6">
                  {currentVideoUrl ? (
                    <div className="w-full h-full relative">
                      {/* Video Thumbnail/Preview */}
                      <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center rounded-lg">
                        <div className="text-center text-white">
                          <div className="p-4 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span className="text-white font-bold text-xl">X</span>
                          </div>
                          <h3 className="text-lg font-semibold mb-2">Xacquisition360</h3>
                        </div>
                      </div>

                      {/* Play Button Overlay */}
                      {!isVideoPlaying && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Button
                            onClick={handleResume}
                            className="bg-white text-black hover:bg-gray-200 px-6 py-3 rounded-lg font-semibold flex items-center gap-2"
                          >
                            <Play className="h-5 w-5 fill-black" />
                            Retomar
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center rounded-lg">
                      <div className="text-center text-white">
                        <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-white font-bold text-xl">X</span>
                        </div>
                        <h3 className="text-lg font-semibold">Xacquisition360</h3>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Bottom Section - Course Content */}
              <div className="bg-gray-800 p-6">
                {/* Section Title */}
                <h3 className="text-xl font-bold text-white mb-2">
                  {currentModule?.title || course.title}
                </h3>

                {/* Course Metadata */}
                <p className="text-gray-400 text-sm mb-4">
                  {course.modules.length} módulo{course.modules.length > 1 ? 's' : ''} • {totalLessons} aula{totalLessons > 1 ? 's' : ''}
                </p>

                {/* Module Selector */}
                <div className="mb-4">
                  <div className="bg-gray-700 rounded-lg p-3 flex items-center justify-between cursor-pointer">
                    <span className="text-white font-medium">
                      {currentModule?.title || course.title}
                    </span>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>
                </div>

                {/* Lesson List */}
                <div className="space-y-2">
                  {currentModule?.lessons.map((lesson) => (
                    <div
                      key={lesson.id}
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-700 transition-colors cursor-pointer"
                      onClick={() => handlePlayLesson(lesson)}
                    >
                      {/* Checkbox */}
                      <div className="w-5 h-5 rounded border-2 border-gray-500 flex items-center justify-center">
                        {lesson.isCompleted && (
                          <Check className="h-3 w-3 text-green-500" />
                        )}
                      </div>

                      {/* Thumbnail */}
                      <div className="w-12 h-8 bg-gray-600 rounded relative overflow-hidden">
                        <div className="w-full h-full bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center">
                          <span className="text-white font-bold text-xs">X</span>
                        </div>
                        {lesson.isCompleted && (
                          <div className="absolute bottom-0 left-0 right-0 h-1 bg-green-500"></div>
                        )}
                      </div>

                      {/* Lesson Info */}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-white truncate">
                          {lesson.title}
                        </h4>
                      </div>

                      {/* Duration */}
                      <div className="flex items-center gap-1 text-xs text-gray-400">
                        <Clock className="h-3 w-3" />
                        <span>{lesson.duration}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
});
