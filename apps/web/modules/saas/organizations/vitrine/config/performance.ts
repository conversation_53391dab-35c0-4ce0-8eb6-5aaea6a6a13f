// Configurações de performance para o modal de preview do curso

export const MODAL_PERFORMANCE_CONFIG = {
  // Configurações de animação
  animations: {
    duration: 300,
    spring: {
      damping: 25,
      stiffness: 300,
    },
    stagger: 0.1,
  },

  // Configurações de cache
  cache: {
    staleTime: 5 * 60 * 1000, // 5 minutos
    cacheTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  },

  // Configurações de virtualização (para listas grandes)
  virtualization: {
    enabled: true,
    itemHeight: 60, // altura estimada de cada item
    overscan: 5, // número de itens extras para renderizar
  },

  // Configurações de lazy loading
  lazyLoading: {
    enabled: true,
    threshold: 0.1, // porcentagem da tela visível para carregar
  },

  // Configurações de debounce
  debounce: {
    search: 300, // ms para debounce de busca
    resize: 150, // ms para debounce de redimensionamento
  },

  // Configurações de memoização
  memoization: {
    enabled: true,
    maxCacheSize: 100, // número máximo de itens em cache
  },
};

// Função para verificar se o dispositivo é mobile
export const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < 768;
};

// Função para verificar se o dispositivo tem baixa performance
export const isLowPerformanceDevice = () => {
  if (typeof navigator === 'undefined') return false;

  // Verificar se é um dispositivo móvel com hardware limitado
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobile = /mobile|android|iphone|ipad|phone/i.test(userAgent);

  // Verificar se tem pouca memória (aproximação)
  const memoryInfo = (navigator as any).deviceMemory;
  const hasLowMemory = memoryInfo && memoryInfo < 4; // menos de 4GB

  return isMobile && hasLowMemory;
};

// Função para aplicar configurações baseadas no dispositivo
export const getOptimizedConfig = () => {
  const isMobile = isMobileDevice();
  const isLowPerformance = isLowPerformanceDevice();

  return {
    ...MODAL_PERFORMANCE_CONFIG,
    animations: {
      ...MODAL_PERFORMANCE_CONFIG.animations,
      duration: isLowPerformance ? 150 : 300,
      stagger: isLowPerformance ? 0.05 : 0.1,
    },
    virtualization: {
      ...MODAL_PERFORMANCE_CONFIG.virtualization,
      enabled: isMobile || isLowPerformance,
    },
    lazyLoading: {
      ...MODAL_PERFORMANCE_CONFIG.lazyLoading,
      enabled: isMobile || isLowPerformance,
    },
  };
};
