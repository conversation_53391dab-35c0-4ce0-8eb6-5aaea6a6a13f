export interface Lesson {
  id: string;
  title: string;
  duration: string;
  thumbnail?: string;
  isCompleted?: boolean;
  videoUrl?: string;
}

export interface Module {
  id: string;
  title: string;
  lessons: Lesson[];
  isExpanded?: boolean;
}

export interface CoursePreviewData {
  id: string;
  title: string;
  description?: string;
  logo?: string;
  modules: Module[];
  firstLesson?: Lesson;
}

export interface CoursePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  course: CoursePreviewData;
  onPlayLesson?: (lessonId: string) => void;
}
