"use client";

import { useQuery } from "@tanstack/react-query";

interface UserCourse {
	id: string;
	title: string;
	description: string;
	type: "course" | "mentoring" | "video";
	image?: string;
	isAccessible: boolean;
	organizationSlug?: string;
	courseId: string;
	finalTime?: string;
	createdAt: string;
	updatedAt: string;
}

interface UseUserCoursesResponse {
	data: UserCourse[];
	total: number;
}

export const useUserCourses = (organizationSlug?: string) => {
	return useQuery({
		queryKey: ["user-courses", organizationSlug],
		queryFn: async (): Promise<UseUserCoursesResponse> => {
			const params = new URLSearchParams();
			if (organizationSlug) {
				params.append("organizationSlug", organizationSlug);
			}

			const response = await fetch(`/api/user/courses?${params.toString()}`, {
				credentials: "include",
				headers: {
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch user courses");
			}

			return response.json();
		},
		enabled: true,
	});
};
