"use client";

import { UnifiedAdminDashboard } from "./UnifiedAdminDashboard";
import { StudentDashboard } from "./StudentDashboard";
import { useViewMode } from "@saas/shared/contexts/ViewModeContext";

export function DashboardWithViewMode() {
	const { isStudentView } = useViewMode();

	return (
		<div className="space-y-6">
			{isStudentView ? (
				<StudentDashboard />
			) : (
				<UnifiedAdminDashboard />
			)}
		</div>
	);
}
