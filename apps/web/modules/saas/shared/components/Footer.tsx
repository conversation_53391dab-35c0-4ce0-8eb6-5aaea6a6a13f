"use client";
import { Logo } from "@shared/components/Logo";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { cn } from "@ui/lib";
import Link from "next/link";
import { Facebook, Instagram, Twitter, Linkedin, Youtube, ExternalLink, Heart } from "lucide-react";

export function Footer() {
  const { activeOrganization } = useActiveOrganization();


  const settings = {
    memberAreaName: activeOrganization?.name || "Cakto Members",
    primaryColor: "#6366f1",
    footer: {
      enabled: true,
      links: [
        { label: "Política de Privacidade", href: "/privacy" },
        { label: "Termos de Uso", href: "/terms" },
        { label: "Suporte", href: "/support" },
      ],
      socialLinks: [
        { platform: "facebook", href: "#", icon: Facebook },
        { platform: "instagram", href: "#", icon: Instagram },
        { platform: "linkedin", href: "#", icon: Linkedin },
      ],
      supportEmail: "<EMAIL>",
    },
  };

  if (!settings.footer.enabled) return null;

  return (
    <footer className="w-full bg-transparent border-t mt-16  backdrop-blur-sm border-white/5">
      <div className="container mx-auto px-4 py-8">

        <div className="flex flex-col lg:flex-row items-center justify-between gap-6 mb-8">

          <Link href="/" className="hover:opacity-80 transition-opacity">
            <Logo className="h-8 w-auto text-white" />
          </Link>


          <nav className="flex items-center gap-6">
            {settings.footer.links.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="text-gray-300 hover:text-white transition-colors text-sm font-medium"
              >
                {link.label}
              </Link>
            ))}
          </nav>


          <div className="flex items-center gap-6">

            <div className="flex items-center gap-3">
              {settings.footer.socialLinks.map((social) => (
                <Link
                  key={social.platform}
                  href={social.href}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <social.icon className="h-5 w-5" />
                </Link>
              ))}
            </div>



          </div>
        </div>


        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-6 border-t border-white/5">

          <p className="text-gray-400 text-sm">
            © 2025 {settings.memberAreaName}. Todos os direitos reservados.
          </p>


          <div className="flex items-center gap-1 text-gray-400 text-sm">
              <span>Feito com</span>
              <Heart className="h-4 w-4 text-red-500 fill-current" />
              <span>Caio Martins</span>
            </div>
        </div>
      </div>
    </footer>
  );
}
