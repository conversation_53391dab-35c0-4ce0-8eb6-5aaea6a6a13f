"use client";

import { DropdownMenuSub } from "@radix-ui/react-dropdown-menu";
import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { UserAvatar } from "@shared/components/UserAvatar";
import { clearCache } from "@shared/lib/cache";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuPortal,
	DropdownMenuRadioGroup,
	DropdownMenuRadioItem,
	DropdownMenuSeparator,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	BookIcon,
	HardDriveIcon,
	HomeIcon,
	LogOutIcon,
	MoonIcon,
	MoreVerticalIcon,
	SettingsIcon,
	SunIcon,
	MessageSquareIcon,
	HelpCircleIcon,
	UsersIcon,
	ShieldIcon,
	FolderIcon,
	MonitorIcon,
	PlayIcon,
	GraduationCapIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";
import Link from "next/link";
import { useState } from "react";

interface MenuItem {
	label: string;
	href: string;
	icon: React.ElementType;
	roles?: string[];
	isExternal?: boolean;
}

export function UserMenu({ showUserName }: { showUserName?: boolean }) {
	const t = useTranslations();
	const { user } = useSession();
	const { setTheme: setCurrentTheme, theme: currentTheme } = useTheme();
	const [theme, setTheme] = useState<string>(currentTheme ?? "system");

	const colorModeOptions = [
		{
			value: "system",
			label: "System",
			icon: HardDriveIcon,
		},
		{
			value: "light",
			label: "Light",
			icon: SunIcon,
		},
		{
			value: "dark",
			label: "Dark",
			icon: MoonIcon,
		},
	];

	const navigationItems: MenuItem[] = [
		// Navigation items are now in the main NavBar
	];

	const communityItems: MenuItem[] = [
		{
			label: "Comunidade",
			href: "https://chat.whatsapp.com/example",
			icon: MessageSquareIcon,
			roles: ["user", "admin", "owner"],
			isExternal: true,
		},
		{
			label: "Suporte",
			href: "/suporte",
			icon: HelpCircleIcon,
			roles: ["user", "admin", "owner"],
		},
	];

	const adminItems: MenuItem[] = [
		{
			label: "Painel Admin",
			href: "/app",
			icon: ShieldIcon,
			roles: ["admin"],
		},
		{
			label: "Usuários",
			href: "/app/admin/users",
			icon: UsersIcon,
			roles: ["admin"],
		},
	];

	const onLogout = () => {
		authClient.signOut({
			fetchOptions: {
				onSuccess: async () => {
					await clearCache();
					window.location.href = new URL(
						config.auth.redirectAfterLogout,
						window.location.origin,
					).toString();
				},
			},
		});
	};

	if (!user) {
		return null;
	}

	const { name, email, image, role } = user;

	const filteredNavigationItems = navigationItems.filter(
		(item) => !item.roles || item.roles.includes(role || "user")
	);

	const filteredCommunityItems = communityItems.filter(
		(item) => !item.roles || item.roles.includes(role || "user")
	);

	const filteredAdminItems = adminItems.filter(
		(item) => !item.roles || item.roles.includes(role || "user")
	);

	return (
		<DropdownMenu modal={false}>
			<DropdownMenuTrigger asChild>
				<button
					type="button"
					className="flex cursor-pointer w-full items-center justify-between gap-2 rounded-full outline-hidden focus-visible:ring-2 focus-visible:ring-primary md:w-[100%+1rem] md:px-2 md:py-1.5 md:hover:bg-primary/5"
					aria-label="User menu"
				>
					<span className="flex items-center gap-2">
						<UserAvatar className="rounded-full" name={name ?? ""} avatarUrl={image} />
						{showUserName && (
							<span className="text-left leading-tight">
								<span className="font-medium text-sm">
									{name}
								</span>
								<span className="block text-xs opacity-70">
									{email}
								</span>
							</span>
						)}
					</span>

					{showUserName && <MoreVerticalIcon className="size-4" />}
				</button>
			</DropdownMenuTrigger>

			<DropdownMenuContent align="end" className="w-64 p-2">
				<div className="px-2 py-3 border-b border-border/50">
					<div className="flex items-center gap-3">
						<UserAvatar className="size-10 rounded-full" name={name ?? ""} avatarUrl={image} />
						<div className="flex-1 min-w-0">
							<p className="font-medium text-sm truncate">{name}</p>
							<p className="text-xs text-muted-foreground truncate">{email}</p>
							<div className="flex items-center gap-1 mt-1">
								<div className="size-2 bg-green-500 rounded-full" />
								<span className="text-xs text-muted-foreground capitalize">{role}</span>
							</div>
						</div>
					</div>
				</div>

				{filteredNavigationItems.length > 0 && (
					<div className="py-2">
						{filteredNavigationItems.map((item) => (
							<DropdownMenuItem key={item.href} asChild className="px-2 py-2.5 rounded-md">
								<Link href={item.href} className="flex items-center gap-3">
									<item.icon className="size-4 text-muted-foreground" />
									<span className="font-medium">{item.label}</span>
								</Link>
							</DropdownMenuItem>
						))}
					</div>
				)}

				{filteredCommunityItems.length > 0 && (
					<>
						{/* <DropdownMenuSeparator className="my-2" /> */}
						<div className="py-2">

							{filteredCommunityItems.map((item) => (
								<DropdownMenuItem key={item.href} asChild className="px-2 py-2.5 rounded-md">
									{item.isExternal ? (
										<a
											href={item.href}
											target="_blank"
											rel="noopener noreferrer"
											className="flex items-center gap-3"
										>
											<item.icon className="size-4 text-muted-foreground" />
											<span>{item.label}</span>
										</a>
									) : (
										<Link href={item.href} className="flex items-center gap-3">
											<item.icon className="size-4 text-muted-foreground" />
											<span>{item.label}</span>
										</Link>
									)}
								</DropdownMenuItem>
							))}
						</div>
					</>
				)}

				{filteredAdminItems.length > 0 && (
					<>
						<DropdownMenuSeparator className="my-2" />
						<div className="py-2">
							<div className="px-2 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wider">
								Administração
							</div>
							{filteredAdminItems.map((item) => (
								<DropdownMenuItem key={item.href} asChild className="px-2 py-2.5 rounded-md">
									<Link href={item.href} className="flex items-center gap-3">
										<item.icon className="size-4 text-muted-foreground" />
										<span>{item.label}</span>
									</Link>
								</DropdownMenuItem>
							))}
						</div>
					</>
				)}

				<DropdownMenuSeparator className="my-2" />

				<DropdownMenuItem asChild className="px-2 py-2.5 rounded-md">
					<Link href="/app/settings/personal" className="flex items-center gap-3">
						<SettingsIcon className="size-4 text-muted-foreground" />
						<span>{t("app.userMenu.accountSettings")}</span>
					</Link>
				</DropdownMenuItem>

				<DropdownMenuSeparator className="my-2" />

				<DropdownMenuItem onClick={onLogout} className="px-2 py-2.5 rounded-md text-red-600 focus:text-red-600">
					<LogOutIcon className="mr-3 size-4" />
					<span>{t("app.userMenu.logout")}</span>
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
