"use client";

import { createContext, useContext, useState, ReactNode } from "react";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";

type ViewMode = "admin" | "student";

interface ViewModeContextType {
	viewMode: ViewMode;
	setViewMode: (mode: ViewMode) => void;
	isStudentView: boolean;
	toggleViewMode: () => void;
	canToggleViewMode: boolean;
}

const ViewModeContext = createContext<ViewModeContextType | undefined>(undefined);

interface ViewModeProviderProps {
	children: ReactNode;
	defaultMode?: ViewMode;
}

export function ViewModeProvider({ children, defaultMode = "admin" }: ViewModeProviderProps) {
	const [viewMode, setViewMode] = useState<ViewMode>(defaultMode);
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();

	const isStudentView = viewMode === "student";

	const canToggleViewMode = Boolean(user?.role === "admin" ||
		(activeOrganization && isOrganizationAdmin(activeOrganization, user)));

	const toggleViewMode = () => {
		if (!canToggleViewMode) return;
		setViewMode(current => current === "admin" ? "student" : "admin");
	};

	return (
		<ViewModeContext.Provider value={{
			viewMode,
			setViewMode,
			isStudentView,
			toggleViewMode,
			canToggleViewMode
		}}>
			{children}
		</ViewModeContext.Provider>
	);
}

export function useViewMode() {
	const context = useContext(ViewModeContext);
	if (context === undefined) {
		throw new Error("useViewMode must be used within a ViewModeProvider");
	}
	return context;
}
