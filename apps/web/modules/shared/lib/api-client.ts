import type { AppRouter } from "@repo/api";
import { getBaseUrl } from "@repo/utils";
import { hc } from "hono/client";

// Função para obter a URL da API
function getApiUrl() {
	// Em desenvolvimento, usar a porta 3001 para a API
	if (process.env.NODE_ENV === "development") {
		return "http://localhost:3001";
	}

	// Em produção, usar a mesma URL base
	return getBaseUrl();
}

export const apiClient = hc<AppRouter>(getApiUrl(), {
	init: {
		credentials: "include",
	},
}).api;
