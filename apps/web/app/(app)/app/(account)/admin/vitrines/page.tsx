"use client";

import { useState, use<PERSON>em<PERSON>, use<PERSON>allback } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Input } from "@ui/components/input";
import {
	PlusIcon,
	PlayIcon,
	UsersIcon,
	BarChart3Icon,
	TrendingUpIcon,
	EyeIcon,
	EditIcon,
	TrashIcon,
	ImageIcon,
	AlertTriangleIcon,
	LoaderIcon,
	AlertCircleIcon,
	Search,
	Filter,
	ArrowRightIcon,
} from "lucide-react";
import { toast } from "sonner";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@ui/components/select";
import { AdminPageLayout } from "@saas/admin/component/shared/AdminPageLayout";
import { useAdminVitrines } from "./hooks/useAdminVitrines";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";

function formatCurrency(value: number): string {
	return new Intl.NumberFormat("pt-BR", {
		style: "currency",
		currency: "BRL",
	}).format(value);
}

function formatDate(date: string | Date): string {
	const dateObj = typeof date === "string" ? new Date(date) : date;
	return dateObj.toLocaleDateString("pt-BR", {
		day: "2-digit",
		month: "2-digit",
		year: "numeric",
	});
}

function VitrineCard({ vitrine, onView, onEdit, onDelete }: {
	vitrine: any;
	onView: (vitrine: any) => void;
	onEdit: (vitrine: any) => void;
	onDelete: (vitrine: any) => void;
}) {
	const getStatusVariant = (status: string) => {
		switch (status) {
			case "PUBLISHED":
				return "success";
			case "DRAFT":
				return "warning";
			case "ARCHIVED":
				return "muted";
			default:
				return "muted";
		}
	};

	const getStatusLabel = (status: string) => {
		switch (status) {
			case "PUBLISHED":
				return "Publicado";
			case "DRAFT":
				return "Rascunho";
			case "ARCHIVED":
				return "Arquivado";
			default:
				return status;
		}
	};

	const getVisibilityLabel = (visibility: string) => {
		switch (visibility) {
			case "PUBLIC":
				return "Público";
			case "PRIVATE":
				return "Privado";
			default:
				return visibility;
		}
	};

	return (
		<Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border hover:border-primary/30 overflow-hidden">
			<div className="p-6">
				{/* Header */}
				<div className="flex items-start justify-between mb-4">
					<div className="flex items-center gap-3">
						<div className="size-12 bg-primary/10 rounded-lg flex items-center justify-center">
							<PlayIcon className="h-6 w-6 text-primary" />
						</div>
						<div className="min-w-0 flex-1">
							<h3 className="font-semibold text-lg text-foreground truncate">
								{vitrine.title}
							</h3>
							<div className="flex items-center gap-2 mt-1">
								<Badge className={getStatusVariant(vitrine.status) === "success" ? "bg-green-100 text-green-800 border-green-200" :
									getStatusVariant(vitrine.status) === "warning" ? "bg-yellow-100 text-yellow-800 border-yellow-200" :
									"bg-gray-100 text-gray-800 border-gray-200"}>
									{getStatusLabel(vitrine.status)}
								</Badge>
								 
								<span className="text-xs text-muted-foreground">
									Criado em {formatDate(vitrine.createdAt)}
								</span>
							</div>
						</div>
					</div>
				</div>

				{/* Banner Image Section */}
				<div className="mb-4">
					{vitrine.bannerImage ? (
						<img
							src={vitrine.bannerImage}
							alt={vitrine.title}
							className="w-full h-32 object-cover rounded-lg"
						/>
					) : (
						<div className="w-full h-32 rounded-lg bg-gradient-to-br from-primary/20 via-primary/10 to-muted/30 flex items-center justify-center">
							<ImageIcon className="h-8 w-8 text-muted-foreground/50" />
						</div>
					)}
				</div>

				{vitrine.description && (
					<div className="mb-4">
						<p className="text-sm text-muted-foreground line-clamp-2">
							{vitrine.description}
						</p>
					</div>
				)}

				<div className="flex gap-2 mt-auto">
					<Button
						onClick={() => onView(vitrine)}
						className="flex-1   hover:bg-primary  transition-all duration-300"
						variant="outline"
						size="sm"
					>
						<EyeIcon className="mr-2 h-4 w-4" />
						Ver
					</Button>
					<Button
						onClick={() => onEdit(vitrine)}
						className="flex-1  hover:bg-primary   transition-all duration-300"
						variant="outline"
						size="sm"
					>
						<EditIcon className="mr-2 h-4 w-4" />
						Editar
					</Button>
					<Button
						onClick={() => onDelete(vitrine)}
						className="text-destructive hover:text-destructive hover:bg-destructive/10 transition-all duration-300"
						variant="outline"
						size="sm"
					>
						<TrashIcon className="h-4 w-4" />
					</Button>
				</div>
			</div>
		</Card>
	);
}

export default function AdminVitrinesPage() {
	const router = useRouter();
	const [selectedOrg, setSelectedOrg] = useState<string>("all");
	const [statusFilter, setStatusFilter] = useState<string>("all");
	const [visibilityFilter, setVisibilityFilter] = useState<string>("all");
	const [searchTerm, setSearchTerm] = useState("");
	const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
	const [vitrineToDelete, setVitrineToDelete] = useState<any>(null);

	const {
		data: vitrinesResponse,
		isLoading,
		error,
		refetch,
	} = useAdminVitrines({
		organizationSlug: selectedOrg === "all" ? undefined : selectedOrg,
		status: statusFilter === "all" ? undefined : (statusFilter.toUpperCase() as any),
		visibility: visibilityFilter === "all" ? undefined : (visibilityFilter.toUpperCase() as any),
		query: searchTerm,
	});

	const vitrines = vitrinesResponse?.data?.vitrines || [];
	const organizations = vitrinesResponse?.data?.organizations || [];
	const stats = vitrinesResponse?.data?.stats || {
		total: 0,
		published: 0,
		views: 0,
		revenue: 0,
	};

	const handleCreateVitrine = useCallback(() => {
		router.push("/app/admin/vitrines/create");
	}, [router]);

	const handleViewVitrine = useCallback((vitrine: any) => {
		window.open(`/app/${vitrine.organization.slug}`, "_blank");
	}, []);

	const handleEditVitrine = useCallback((vitrine: any) => {
		router.push(`/app/admin/vitrines/${vitrine.id}/edit`);
	}, [router]);

	const handleDeleteVitrine = useCallback((vitrine: any) => {
		setVitrineToDelete(vitrine);
		setDeleteConfirmOpen(true);
	}, []);

	const confirmDeleteVitrine = useCallback(async () => {
		if (vitrineToDelete) {
			try {
				toast.success(`Vitrine "${vitrineToDelete.title}" removida com sucesso`);
				setDeleteConfirmOpen(false);
				setVitrineToDelete(null);
				refetch();
			} catch (error) {
				toast.error("Erro ao remover a vitrine. Tente novamente.");
			}
		}
	}, [vitrineToDelete, refetch]);

	const clearFilters = () => {
		setSelectedOrg("all");
		setStatusFilter("all");
		setVisibilityFilter("all");
		setSearchTerm("");
	};

	const hasActiveFilters = selectedOrg !== "all" || statusFilter !== "all" || visibilityFilter !== "all" || searchTerm;

	return (
		<>
			<AdminPageLayout
				title="Gestão de Vitrines"
				subtitle="Gerencie todas as vitrines das organizações"
				actionButton={{
					label: "Nova Vitrine",
					onClick: handleCreateVitrine,
					icon: <PlusIcon className="mr-2 h-4 w-4" />,
				}}
			>
				{/* Stats Overview */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
					<Card className="hover:shadow-md transition-shadow">
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Total de Vitrines
									</p>
									<div className="flex items-center gap-2 mt-2">
										<p className="text-2xl font-bold text-foreground">
											{stats.total}
										</p>
									</div>
								</div>
								<div className="p-3 rounded-full bg-primary/10">
									<BarChart3Icon className="h-6 w-6 text-primary" />
								</div>
							</div>
						</CardContent>
					</Card>
					<Card className="hover:shadow-md transition-shadow">
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Publicadas
									</p>
									<div className="flex items-center gap-2 mt-2">
										<p className="text-2xl font-bold text-foreground">
											{stats.published}
										</p>
									</div>
								</div>
								<div className="p-3 rounded-full bg-primary/10">
									<EyeIcon className="h-6 w-6 text-primary" />
								</div>
							</div>
						</CardContent>
					</Card>
					<Card className="hover:shadow-md transition-shadow">
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Total de Visualizações
									</p>
									<div className="flex items-center gap-2 mt-2">
										<p className="text-2xl font-bold text-foreground">
											{stats.views}
										</p>
									</div>
								</div>
								<div className="p-3 rounded-full bg-primary/10">
									<UsersIcon className="h-6 w-6 text-primary" />
								</div>
							</div>
						</CardContent>
					</Card>
					<Card className="hover:shadow-md transition-shadow">
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Receita Total
									</p>
									<div className="flex items-center gap-2 mt-2">
										<p className="text-2xl font-bold text-foreground">
											{formatCurrency(stats.revenue)}
										</p>
									</div>
								</div>
								<div className="p-3 rounded-full bg-primary/10">
									<TrendingUpIcon className="h-6 w-6 text-primary" />
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				<div className="flex flex-col sm:flex-row gap-4 mb-6">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
						<Input
							type="search"
							placeholder="Buscar por título, descrição ou organização..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-9"
						/>
					</div>

					<div className="flex gap-4">
						<Select value={selectedOrg} onValueChange={setSelectedOrg}>
							<SelectTrigger className="w-48">
								<SelectValue placeholder="Workspaces" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Workspaces</SelectItem>
								{organizations.map((org: any) => (
									<SelectItem key={org.slug} value={org.slug}>
										{org.name} ({org.vitrinesCount} vitrines)
									</SelectItem>
								))}
							</SelectContent>
						</Select>

						<Select value={statusFilter} onValueChange={setStatusFilter}>
							<SelectTrigger className="w-48">
								<SelectValue placeholder="Status" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Status</SelectItem>
								<SelectItem value="PUBLISHED">Publicado</SelectItem>
								<SelectItem value="DRAFT">Rascunho</SelectItem>
								<SelectItem value="ARCHIVED">Arquivado</SelectItem>
							</SelectContent>
						</Select>

						<div className="hidden sm:block"> 
							<Select value={visibilityFilter}  onValueChange={setVisibilityFilter}>
							<SelectTrigger className="w-48">
								<SelectValue placeholder="Visibilidade" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Visibilidade</SelectItem>
								<SelectItem value="PUBLIC">Público</SelectItem>
								<SelectItem value="PRIVATE">Privado</SelectItem>
							</SelectContent>
						</Select>
						</div>

						{hasActiveFilters && (
							<Button variant="outline" onClick={clearFilters}>
								<Filter className="mr-2 h-4 w-4" />
								Limpar Filtros
							</Button>
						)}
					</div>
				</div>


				{isLoading ? (
					<Card>
						<CardContent className="py-12">
							<div className="flex items-center justify-center">
								<LoaderIcon className="h-8 w-8 animate-spin text-muted-foreground" />
								<span className="ml-2 text-muted-foreground">Carregando vitrines...</span>
							</div>
						</CardContent>
					</Card>
				) : error ? (

					<Card>
						<CardContent className="py-12">
							<div className="flex flex-col items-center justify-center">
								<AlertCircleIcon className="h-12 w-12 text-red-500 mb-4" />
								<h3 className="text-lg font-semibold mb-2">Erro ao carregar vitrines</h3>
								<p className="text-muted-foreground text-center mb-4">{error.message}</p>
								<Button onClick={() => refetch()} variant="outline">
									Tentar novamente
								</Button>
							</div>
						</CardContent>
					</Card>
				) : vitrines.length === 0 ? (

					<Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
						<CardContent className="pt-6">
							<div className="text-center py-8">
								<PlayIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
								<h3 className="text-lg font-medium text-white mb-2">Nenhuma vitrine encontrada</h3>
								<p className="text-white/40 mb-4">
									{hasActiveFilters
										? "Tente ajustar os filtros de busca"
										: "Comece criando sua primeira vitrine para exibir seus cursos"}
								</p>
								<div className="flex justify-center">
									<Button onClick={handleCreateVitrine}>
										<PlusIcon className="h-4 w-4 mr-2" />
										Criar Primeira Vitrine
									</Button>
								</div>
							</div>
						</CardContent>
					</Card>
				) : (

					<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
						{vitrines.map((vitrine: any) => (
							<VitrineCard
								key={vitrine.id}
								vitrine={vitrine}
								onView={handleViewVitrine}
								onEdit={handleEditVitrine}
								onDelete={handleDeleteVitrine}
							/>
						))}
					</div>
				)}
			</AdminPageLayout>


			<AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle className="flex items-center gap-2">
							<AlertTriangleIcon className="h-5 w-5 text-destructive" />
							Confirmar exclusão
						</AlertDialogTitle>
						<AlertDialogDescription>
							{vitrineToDelete && (
								<>
									Tem certeza que deseja excluir a vitrine <strong>{vitrineToDelete.title}</strong>?
									<br />
									Esta ação não pode ser desfeita.
								</>
							)}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancelar</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmDeleteVitrine}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Excluir
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
