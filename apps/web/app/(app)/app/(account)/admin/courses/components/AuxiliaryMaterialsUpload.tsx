"use client";

import { useCallback, useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Progress } from "@ui/components/progress";
import { Badge } from "@ui/components/badge";
import {
	UploadIcon,
	FileIcon,
	XIcon,
	LoaderIcon,
	AlertCircleIcon,
	PlusIcon,
	TrashIcon,
} from "lucide-react";
import { useS3FileUpload } from "../hooks/useS3FileUpload";
import type { CourseLessonFile } from "../types";

// Type alias for backward compatibility
type AuxiliaryMaterial = CourseLessonFile;
import { toast } from "sonner";

interface AuxiliaryMaterialsUploadProps {
	value: AuxiliaryMaterial[];
	onChange: (materials: AuxiliaryMaterial[]) => void;
	organizationId: string;
	label?: string;
	description?: string;
	className?: string;
}

export function AuxiliaryMaterialsUpload({
	value = [],
	onChange,
	organizationId,
	label = "Materiais Auxiliares",
	description = "Faça upload de PDFs, documentos e outros materiais de apoio",
	className = "",
}: AuxiliaryMaterialsUploadProps) {
	const [isDragOver, setIsDragOver] = useState(false);
	const [uploadingFiles, setUploadingFiles] = useState<Map<string, { size: number }>>(new Map());

	const fileUpload = useS3FileUpload({
		organizationId,
		onSuccess: (fileUrl: string, fileName?: string) => {
			// Find the file size from the uploading files
			const fileInfo = uploadingFiles.get(fileName || '');
			const fileSize = fileInfo?.size || 0;

			const newMaterial: AuxiliaryMaterial = {
				id: `temp-${Date.now()}`,
				name: fileName || 'Unknown file',
				url: fileUrl,
				type: getFileType(fileName || ''),
				size: fileSize,
				status: 'completed',
			};

			onChange([...value, newMaterial]);
			setUploadingFiles(prev => {
				const next = new Map(prev);
				next.delete(fileName || '');
				return next;
			});
		},
		onError: (error: string, fileName?: string) => {
			if (fileName) {
				setUploadingFiles(prev => {
					const next = new Map(prev);
					next.delete(fileName);
					return next;
				});
			}
		},
	});

	const getFileType = (fileName: string): AuxiliaryMaterial['type'] => {
		const extension = fileName.split('.').pop()?.toLowerCase();
		switch (extension) {
			case 'pdf':
				return 'pdf';
			case 'doc':
			case 'docx':
			case 'txt':
			case 'rtf':
				return 'document';
			case 'jpg':
			case 'jpeg':
			case 'png':
			case 'gif':
			case 'webp':
				return 'image';
			case 'mp4':
			case 'avi':
			case 'mov':
			case 'wmv':
				return 'video';
			case 'mp3':
			case 'wav':
			case 'aac':
				return 'audio';
			default:
				return 'other';
		}
	};

	const handleFileSelect = useCallback(
		async (files: FileList) => {
			const fileArray = Array.from(files);

			for (const file of fileArray) {
				// Check file size (max 50MB)
				if (file.size > 50 * 1024 * 1024) {
					toast.error(`Arquivo ${file.name} é muito grande. Máximo 50MB.`);
					continue;
				}

				setUploadingFiles(prev => new Map(prev).set(file.name, { size: file.size }));

				try {
					await fileUpload.uploadFile(file, 'course-materials');
				} catch (error) {

				}
			}
		},
		[fileUpload]
	);

	const handleDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragOver(true);
	}, []);

	const handleDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragOver(false);
	}, []);

	const handleDrop = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			setIsDragOver(false);
			const files = e.dataTransfer.files;
			if (files.length > 0) {
				handleFileSelect(files);
			}
		},
		[handleFileSelect]
	);

	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const files = e.target.files;
			if (files && files.length > 0) {
				handleFileSelect(files);
			}
			// Reset input value to allow selecting the same file again
			e.target.value = '';
		},
		[handleFileSelect]
	);

	const removeMaterial = useCallback(
		(materialId: string) => {
			onChange(value.filter(material => material.id !== materialId));
		},
		[value, onChange]
	);

	const getFileIcon = (type: AuxiliaryMaterial['type']) => {
		switch (type) {
			case 'pdf':
				return <FileIcon className="h-4 w-4 text-red-500" />;
			case 'document':
				return <FileIcon className="h-4 w-4 text-blue-500" />;
			case 'image':
				return <FileIcon className="h-4 w-4 text-green-500" />;
			case 'video':
				return <FileIcon className="h-4 w-4 text-purple-500" />;
			case 'audio':
				return <FileIcon className="h-4 w-4 text-orange-500" />;
			default:
				return <FileIcon className="h-4 w-4 text-gray-500" />;
		}
	};

	const formatFileSize = (bytes: number) => {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	};

	return (
		<div className={`space-y-4 ${className}`}>
			<div>
				<label className="text-sm font-medium text-gray-700 dark:text-gray-300">
					{label}
				</label>
				{description && (
					<p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
						{description}
					</p>
				)}
			</div>

			{/* Upload Area */}
			<div
				className={`
					border-2 border-dashed rounded-lg p-6 text-center transition-colors
					${isDragOver
						? 'border-blue-400 bg-blue-50 dark:bg-blue-950/20'
						: 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
					}
				`}
				onDragOver={handleDragOver}
				onDragLeave={handleDragLeave}
				onDrop={handleDrop}
			>
				<div className="flex flex-col items-center space-y-2">
					<UploadIcon className="h-8 w-8 text-gray-400" />
					<div>
						<p className="text-sm text-gray-600 dark:text-gray-400">
							Arraste arquivos aqui ou{" "}
							<label className="text-blue-600 hover:text-blue-500 cursor-pointer font-medium">
								clique para selecionar
								<Input
									type="file"
									multiple
									className="hidden"
									onChange={handleInputChange}
									accept=".pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.wmv,.mp3,.wav,.aac"
								/>
							</label>
						</p>
						<p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
							Máximo 50MB por arquivo
						</p>
					</div>
				</div>
			</div>

			{/* Uploading Files */}
			{Array.from(uploadingFiles.keys()).map((fileName) => (
				<div key={fileName} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
					<LoaderIcon className="h-4 w-4 animate-spin text-blue-500" />
					<div className="flex-1">
						<p className="text-sm font-medium text-gray-900 dark:text-gray-100">
							{fileName}
						</p>
						<p className="text-xs text-gray-500 dark:text-gray-400">
							Enviando...
						</p>
					</div>
				</div>
			))}

			{/* Uploaded Materials */}
			{value.length > 0 && (
				<div className="space-y-2">
					<h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
						Materiais Carregados ({value.length})
					</h4>
					{value.map((material) => (
						<div
							key={material.id}
							className="flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
						>
							{getFileIcon(material.type)}
							<div className="flex-1 min-w-0">
								<p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
									{material.name}
								</p>
								<div className="flex items-center space-x-2 mt-1">
									<Badge status="info" className="text-xs">
										{material.type.toUpperCase()}
									</Badge>
									{material.size > 0 && (
										<span className="text-xs text-gray-500 dark:text-gray-400">
											{formatFileSize(material.size)}
										</span>
									)}
								</div>
							</div>
							<Button
								type="button"
								variant="ghost"
								size="sm"
								onClick={() => removeMaterial(material.id!)}
								className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
							>
								<TrashIcon className="h-4 w-4" />
							</Button>
						</div>
					))}
				</div>
			)}

			{fileUpload.uploadState.status === 'error' && (
				<div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
					<AlertCircleIcon className="h-4 w-4 text-red-500" />
					<p className="text-sm text-red-700 dark:text-red-400">
						{fileUpload.uploadState.error}
					</p>
				</div>
			)}
		</div>
	);
}
