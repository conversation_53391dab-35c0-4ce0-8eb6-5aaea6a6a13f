import { getSession } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { UnifiedAdminDashboard } from "@saas/start/components/UnifiedAdminDashboard";
import { StudentDashboard } from "@saas/start/components/StudentDashboard";
import { ViewModeProvider } from "@saas/shared/contexts/ViewModeContext";
import { DashboardWithViewMode } from "@saas/start/components/DashboardWithViewMode";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";

export default async function AppStartPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	const t = await getTranslations();

	// Verificar o role do usuário
	const userRole = session.user?.role;
	const isStudent = userRole === "user" || userRole === null || userRole === undefined;
	const isAdmin = userRole === "admin";

	return (
		<div className="">
			{isStudent ? (
				<StudentDashboard />
			) : (
				<ViewModeProvider defaultMode="admin">
					<DashboardWithViewMode />
				</ViewModeProvider>
			)}
		</div>
	);
}
