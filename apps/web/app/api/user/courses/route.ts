import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
	try {
		const session = await getSession();

		if (!session?.user) {
			return NextResponse.json(
				{ error: "Unauthorized" },
				{ status: 401 }
			);
		}

		const { searchParams } = new URL(request.url);
		const organizationSlug = searchParams.get("organizationSlug");

		// Buscar os cursos do usuário através do modelo UserCourses
		const userCourses = await db.userCourses.findMany({
			where: {
				userId: session.user.id,
				...(organizationSlug && {
					course: {
						organization: {
							slug: organizationSlug
						}
					}
				})
			},
			include: {
				course: {
					include: {
						organization: true
					}
				}
			},
			orderBy: {
				createdAt: "desc"
			}
		});

		// Transformar os dados para o formato esperado pelo frontend
		const formattedCourses = userCourses.map((userCourse) => ({
			id: userCourse.course.id,
			title: userCourse.course.name,
			description: userCourse.course.description || "Curso disponível na plataforma",
			type: "course" as const,
			image: userCourse.course.logo,
			isAccessible: true, // Se está em UserCourses, o usuário tem acesso
			organizationSlug: userCourse.course.organization.slug,
			courseId: userCourse.courseId,
			finalTime: userCourse.finalTime?.toISOString(),
			createdAt: userCourse.createdAt.toISOString(),
			updatedAt: userCourse.updatedAt.toISOString(),
		}));

		return NextResponse.json({
			data: formattedCourses,
			total: formattedCourses.length
		});

	} catch (error) {
		console.error("Error fetching user courses:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 }
		);
	}
}
