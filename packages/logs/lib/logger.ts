import { createConsola } from "consola";

export const logger = createConsola({
	formatOptions: {
		date: true,
		colors: process.env.NODE_ENV !== "production",
	},
	level: (process.env.LOG_LEVEL as any) || (process.env.NODE_ENV === "production" ? "info" : "debug"),
});

// Ensure logs are flushed in production
if (process.env.NODE_ENV === "production") {
	process.on("exit", () => {
		logger.info("Application shutting down");
	});

	process.on("uncaughtException", (error) => {
		logger.error("Uncaught Exception:", error);
		process.exit(1);
	});

	process.on("unhandledRejection", (reason, promise) => {
		logger.error("Unhandled Rejection at:", promise, "reason:", reason);
		process.exit(1);
	});
}
