import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const createVitrineSchema = z.object({
	title: z.string().min(1, "Title is required"),
	description: z.string().optional().nullable(),
	status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).default("DRAFT"),
	visibility: z.enum(["PUBLIC", "PRIVATE"]).default("PUBLIC"),
	bannerImage: z.string().optional().or(z.literal("")).nullable(),
	isDefault: z.boolean().default(false),
	organizationId: z.string().min(1, "Organization ID is required"),
	sections: z
		.array(
			z.object({
				title: z.string().min(1, "Section title is required"),
				subtitle: z.string().optional().nullable(),
				description: z.string().optional().nullable(),
				position: z.number().int().min(0),
				isLocked: z.boolean().default(false),
				requiresPurchase: z.boolean().default(false),
				checkoutUrl: z.string().optional().or(z.literal("")).nullable(),
				webhookUrl: z.string().optional().or(z.literal("")).nullable(),
				price: z.number().positive().optional().nullable(),
				originalPrice: z.number().positive().optional().nullable(),
				accessType: z.enum(["FREE", "PAID", "MEMBER_ONLY"]).default("FREE"),
				visibility: z.enum(["PUBLIC", "PRIVATE"]).default("PUBLIC"),
				courses: z
					.array(
						z.object({
							courseId: z.string(),
							position: z.number().int().min(0),
						}),
					)
					.default([]),
			}),
		)
		.default([]),
});

export const createVitrine = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.post("/", validator("json", createVitrineSchema),
		describeRoute({
			summary: "Create a new vitrine",
			tags: ["Vitrines"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const data = c.req.valid("json");
			const { sections, organizationId, ...vitrineData } = data;

			// Verificar se o usuário tem permissão na organização
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can create vitrines" }, 403);
			}

			// Validar cursos existentes e se pertencem à organização
			const courseIds = sections.flatMap((section) => section.courses.map((c) => c.courseId));
			if (courseIds.length > 0) {
				const existingCourses = await db.courses.findMany({
					where: {
						id: { in: courseIds },
						organizationId,
					},
					select: { id: true },
				});

				const existingCourseIds = existingCourses.map((c) => c.id);
				const invalidCourseIds = courseIds.filter((id) => !existingCourseIds.includes(id));

				if (invalidCourseIds.length > 0) {
					return c.json(
						{
							error: "Some courses do not exist or do not belong to this organization",
							invalidCourseIds,
						},
						400,
					);
				}
			}

			// If this vitrine is being set as default, unset any existing default vitrines
			if (data.isDefault) {
				await db.vitrine.updateMany({
					where: {
						organizationId,
						isDefault: true,
					},
					data: {
						isDefault: false,
					},
				});
			}

			// Criar vitrine com seções e cursos
			const vitrine = await db.vitrine.create({
				data: {
					...vitrineData,
					organizationId,
					createdBy: user.id,
					sections: {
						create: sections.map((section) => ({
							title: section.title,
							subtitle: section.subtitle,
							description: section.description,
							position: section.position,
							isLocked: section.isLocked,
							requiresPurchase: section.requiresPurchase,
							checkoutUrl: section.checkoutUrl,
							webhookUrl: section.webhookUrl,
							price: section.price,
							originalPrice: section.originalPrice,
							accessType: section.accessType,
							visibility: section.visibility,
							courses: {
								create: section.courses.map((course) => ({
									courseId: course.courseId,
									position: course.position,
								})),
							},
						})),
					},
				},
				include: {
					sections: {
						orderBy: { position: "asc" },
						include: {
							courses: {
								orderBy: { position: "asc" },
								include: {
									course: {
										select: {
											id: true,
											name: true,
											logo: true,
										},
									},
								},
							},
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
			});

			const response = {
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				status: vitrine.status,
				visibility: vitrine.visibility,
				bannerImage: vitrine.bannerImage,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organizationId: vitrine.organizationId,
				createdBy: vitrine.createdBy,
				creator: vitrine.creator,
				sections: vitrine.sections.map((section) => ({
					id: section.id,
					title: section.title,
					subtitle: section.subtitle,
					description: section.description,
					position: section.position,
					isLocked: section.isLocked,
					requiresPurchase: section.requiresPurchase,
					checkoutUrl: section.checkoutUrl,
					webhookUrl: section.webhookUrl,
					price: section.price ? Number(section.price) : null,
					originalPrice: section.originalPrice ? Number(section.originalPrice) : null,
					accessType: section.accessType,
					visibility: section.visibility,
					courses: section.courses,
				})),
			};

			return c.json(response, 201);
		} catch (error) {
			console.error("Error creating vitrine:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
