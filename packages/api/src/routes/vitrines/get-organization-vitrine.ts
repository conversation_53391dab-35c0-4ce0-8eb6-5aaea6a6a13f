import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const querySchema = z.object({
	organizationSlug: z.string(),
});

export const getOrganizationVitrine = new Hono()
	.use(authMiddleware)
	.get("/organization/vitrine", validator("query", querySchema),
		describeRoute({
			summary: "Get organization vitrine details",
			tags: ["Vitrines"],
		}),
		async (c) => {
		try {
			const { organizationSlug } = c.req.valid("query");
			const user = c.get("user");

			console.log("🔍 [VITRINE] Starting request for user:", user.email, "org:", organizationSlug);

			// 1. Buscar organização
			const organization = await db.organization.findUnique({
				where: { slug: organizationSlug },
				select: { id: true, name: true, slug: true },
			});

			if (!organization) {
				console.log("❌ [VITRINE] Organization not found:", organizationSlug);
				return c.json({ error: "Organization not found" }, 404);
			}

			console.log("✅ [VITRINE] Organization found:", organization.id);

			// 2. Verificar membership - OWNER, ADMIN e MEMBER têm acesso
			const membership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId: organization.id,
				},
				select: {
					id: true,
					role: true,
					userId: true,
					organizationId: true
				},
			});

			console.log("🔍 [VITRINE] Membership check:", {
				userId: user.id,
				organizationId: organization.id,
				membershipFound: !!membership,
				role: membership?.role
			});

			if (!membership) {
				console.log("❌ [VITRINE] User is not a member of organization");

				// Debug: mostrar todas as memberships do usuário
				const userMemberships = await db.member.findMany({
					where: { userId: user.id },
					include: { organization: { select: { slug: true, name: true } } }
				});

				console.log("🔍 [VITRINE] User memberships:", userMemberships.map(m => ({
					orgSlug: m.organization.slug,
					orgName: m.organization.name,
					role: m.role
				})));

				return c.json({ error: "Forbidden - User is not a member of this organization" }, 403);
			}

			// Verificar se a role é válida (owner, admin ou member)
			const validRoles = ['owner', 'admin', 'member'];
			if (!validRoles.includes(membership.role)) {
				console.log("❌ [VITRINE] Invalid role:", membership.role);
				return c.json({ error: "Forbidden - Invalid role" }, 403);
			}

			console.log("✅ [VITRINE] User has valid access with role:", membership.role);

			// 3. Buscar vitrine publicada da organização
			// First try to find a default vitrine, then fall back to most recent
			let vitrine = await db.vitrine.findFirst({
				where: {
					organizationId: organization.id,
					status: "PUBLISHED",
					visibility: "PUBLIC",
					isDefault: true,
				},
				include: {
					sections: {
						orderBy: { position: "asc" },
						include: {
							courses: {
								orderBy: { position: "asc" },
								include: {
									course: {
										select: {
											id: true,
											name: true,
											logo: true,
											community: true,
											link: true,
											createdAt: true,
											updatedAt: true,
										},
									},
								},
							},
						},
					},
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
							logo: true,
						},
					},
				},
			});

			// If no default vitrine found, get the most recent published one
			if (!vitrine) {
				vitrine = await db.vitrine.findFirst({
					where: {
						organizationId: organization.id,
						status: "PUBLISHED",
						visibility: "PUBLIC",
					},
					include: {
						sections: {
							orderBy: { position: "asc" },
							include: {
								courses: {
									orderBy: { position: "asc" },
									include: {
										course: {
											select: {
												id: true,
												name: true,
												logo: true,
												community: true,
												link: true,
												createdAt: true,
												updatedAt: true,
											},
										},
									},
								},
							},
						},
						organization: {
							select: {
								id: true,
								name: true,
								slug: true,
								logo: true,
							},
						},
					},
					orderBy: { createdAt: "desc" },
				});
			}

			if (!vitrine) {
				console.log("❌ [VITRINE] No published vitrine found for organization");
				return c.json({ error: "Vitrine not found" }, 404);
			}

			console.log("✅ [VITRINE] Vitrine found:", {
				id: vitrine.id,
				title: vitrine.title,
				sectionsCount: vitrine.sections?.length || 0
			});

			// Formatar os dados para o frontend
			const formattedVitrine = {
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				status: vitrine.status,
				visibility: vitrine.visibility,
				bannerImage: vitrine.bannerImage,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organizationId: vitrine.organizationId,
				createdBy: vitrine.createdBy,
				organization: vitrine.organization,
				sections: vitrine.sections.map((section) => ({
					id: section.id,
					title: section.title,
					subtitle: section.subtitle,
					description: section.description,
					position: section.position,
					isLocked: section.isLocked,
					requiresPurchase: section.requiresPurchase,
					checkoutUrl: section.checkoutUrl,
					webhookUrl: section.webhookUrl,
					price: section.price ? Number(section.price) : null,
					originalPrice: section.originalPrice ? Number(section.originalPrice) : null,
					accessType: section.accessType,
					visibility: section.visibility,
					courses: section.courses.map((courseRelation) => ({
						id: courseRelation.course.id, // Usar o ID real do curso
						name: courseRelation.course.name,
						logo: courseRelation.course.logo,
						community: courseRelation.course.community,
						link: courseRelation.course.link,
						createdAt: courseRelation.course.createdAt.toISOString(),
						updatedAt: courseRelation.course.updatedAt.toISOString(),
					})),
				})),
			};

			return c.json({ data: formattedVitrine });

		} catch (error) {
			console.error("❌ [VITRINE] Error:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
