import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";

const paramsSchema = z.object({
	id: z.string(),
});

const querySchema = z.object({
	organizationId: z.string().optional(),
	userAgent: z.string().optional(),
	ipAddress: z.string().optional(),
});

export const getPublicVitrine = new Hono().get(
	"/public/:id",
	validator(
		"param",
		z.object({
			id: z.string(),
		}),
	),
	describeRoute({
		summary: "Get a specific public vitrine by ID",
		tags: ["Vitrines"],
	}),
	validator("query", querySchema),
	async (c) => {
		try {
			const { id } = c.req.valid("param");
			const { organizationId, userAgent, ipAddress } = c.req.valid("query");

			const vitrine = await db.vitrine.findFirst({
				where: {
					id,
					...(organizationId && { organizationId }),
					status: "PUBLISHED",
					visibility: "PUBLIC",
				},
				include: {
					sections: {
						orderBy: { position: "asc" },
						include: {
							courses: {
								orderBy: { position: "asc" },
								include: {
									course: {
										select: {
											id: true,
											name: true,
											logo: true,
											community: true,
											link: true,
											createdAt: true,
											updatedAt: true,
										},
									},
								},
							},
						},
					},
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
							logo: true,
						},
					},
				},
			});

			if (!vitrine) {
				return c.json({ error: "Vitrine not found" }, 404);
			}

			// Registrar visualização da vitrine
			try {
				await db.vitrineView.create({
					data: {
						vitrineId: vitrine.id,
						userAgent,
						ipAddress,
					},
				});
			} catch (viewError) {
				// Log error but don't fail the request
				console.warn("Failed to record vitrine view:", viewError);
			}

			const formattedVitrine = {
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				status: vitrine.status,
				visibility: vitrine.visibility,
				bannerImage: vitrine.bannerImage,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organization: vitrine.organization,
				sections: vitrine.sections.map((section) => ({
					id: section.id,
					title: section.title,
					subtitle: section.subtitle,
					description: section.description,
					position: section.position,
					isLocked: section.isLocked,
					requiresPurchase: section.requiresPurchase,
					checkoutUrl: section.checkoutUrl,
					price: section.price ? Number(section.price) : null,
					originalPrice: section.originalPrice ? Number(section.originalPrice) : null,
					accessType: section.accessType,
					visibility: section.visibility,
					courses: section.courses.map((courseRelation) => ({
						id: courseRelation.course.id,
						name: courseRelation.course.name,
						logo: courseRelation.course.logo,
						community: courseRelation.course.community,
						link: courseRelation.course.link,
						createdAt: courseRelation.course.createdAt.toISOString(),
						updatedAt: courseRelation.course.updatedAt.toISOString(),
					})),
				})),
			};

			return c.json(formattedVitrine);
		} catch (error) {
			console.error("Error fetching public vitrine:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);
