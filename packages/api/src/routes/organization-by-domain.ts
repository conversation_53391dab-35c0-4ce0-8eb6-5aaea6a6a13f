import { db } from "@repo/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";

function extractSubdomain(host: string): string | null {
	const parts = host.split(".");

	// For cakto.com.br subdomains
	if (parts.length >= 4 && parts.slice(-3).join(".") === "cakto.com.br") {
		return parts[0];
	}

	return null;
}

export const organizationByDomainRouter = new Hono()
	.basePath("/organization")
	.get("/by-domain",
		describeRoute({
			summary: "Get organization by domain",
			tags: ["Organizations"],
		}),
		async (c) => {
		try {
			const host = c.req.query("host");
			console.log("Host received:", host);

			if (!host) {
				return c.json({ error: "Host parameter is required" }, 400);
			}

			// Check for custom domain first
			let organization = await db.organization.findFirst({
				where: { customDomain: host },
				include: { members: true, invitations: true },
			});

			if (organization) {
				return c.json(organization);
			}

			// Check for subdomain
			const subdomain = extractSubdomain(host);
			if (subdomain) {
				organization = await db.organization.findFirst({
					where: { subdomain },
					include: { members: true, invitations: true },
				});
			}

			return c.json(organization);
		} catch (error) {
			console.error("Error in organization by-domain route:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
