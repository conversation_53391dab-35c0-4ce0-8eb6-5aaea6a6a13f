import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from '../../middleware/auth'

const paramsSchema = z.object({
  courseId: z.string(),
})

const querySchema = z.object({
  organizationSlug: z.string(),
})

export const getCourse = new Hono()
  .get('/test',
		describeRoute({
			summary: "Test course route",
			tags: ["Courses"],
		}),
		async (c) => {
    console.log('🔍 Course API - Test route hit!')
    return c.json({ message: 'Test route working' })
  })
  .use('*', async (c, next) => {
    console.log('🔍 Course API - Route hit before auth middleware:', c.req.path)
    await next()
  })
  .use(authMiddleware)
  .get('/course/:courseId', validator('param', paramsSchema), validator('query', querySchema),
		describeRoute({
			summary: "Get course by ID with organization",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const user = c.get('user')
      const { courseId } = c.req.valid('param')
      const { organizationSlug } = c.req.valid('query')

      console.log('🔍 Course API - Starting course fetch:', { courseId, organizationSlug, userId: user.id, userRole: user.role })

      // Find organization by slug
      const organization = await db.organization.findFirst({
        where: { slug: organizationSlug },
      })

      console.log('🔍 Course API - Organization found:', organization ? { id: organization.id, name: organization.name } : 'NOT FOUND')

      if (!organization) {
        console.log('❌ Course API - Organization not found for slug:', organizationSlug)
        return c.json({ error: 'Organization not found' }, 404)
      }

      // Check if user is admin - admins should have access to all courses
      if (user.role === 'admin') {
        console.log('✅ Course API - User is admin, bypassing membership check')
      } else {
        // Verify user is member of organization
        const userMembership = await db.member.findFirst({
          where: {
            userId: user.id,
            organizationId: organization.id,
          },
        })

        console.log('🔍 Course API - User membership:', userMembership ? { role: userMembership.role, organizationId: userMembership.organizationId } : 'NOT FOUND')

        if (!userMembership) {
          console.log('❌ Course API - User is not a member of organization')
          return c.json({ error: 'Access denied' }, 403)
        }
      }

      // Buscar o curso com seus módulos e lições
      const course = await db.courses.findFirst({
        where: {
          id: courseId,
          organizationId: organization.id
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            }
          },
          courseModules: {
            include: {
              module: {
                include: {
                  lessons: {
                    orderBy: { position: 'asc' },
                    include: {
                      userWatchedLessons: {
                        where: { userId: user.id },
                        select: {
                          isCompleted: true,
                          currentTime: true,
                          duration: true,
                        }
                      }
                    }
                  }
                }
              }
            },
            orderBy: { module: { position: 'asc' } }
          }
        }
      })

      console.log('🔍 Course API - Course found:', course ? { id: course.id, name: course.name, organizationId: course.organizationId } : 'NOT FOUND')

      if (!course) {
        console.log('❌ Course API - Course not found:', { courseId, organizationId: organization.id })

        // Let's also check if the course exists at all
        const anyCourse = await db.courses.findFirst({
          where: { id: courseId },
          select: { id: true, name: true, organizationId: true }
        })

        if (anyCourse) {
          console.log('🔍 Course API - Course exists but in different organization:', {
            courseId,
            courseOrganizationId: anyCourse.organizationId,
            requestedOrganizationId: organization.id
          })
        } else {
          console.log('🔍 Course API - Course does not exist at all')
        }

        return c.json({ error: 'Course not found' }, 404)
      }

      // Check if user has access to the course (unless admin)
      if (user.role !== 'admin') {
        const userCourseAccess = await db.userCourses.findFirst({
          where: {
            userId: user.id,
            courseId: courseId,
          },
        })

        console.log('🔍 Course API - User course access:', userCourseAccess ? 'FOUND' : 'NOT FOUND')

        if (!userCourseAccess) {
          console.log('❌ Course API - User does not have access to this course')
          return c.json({ error: 'Access denied - You are not enrolled in this course' }, 403)
        }
      }

      // Format course data
      const formattedCourse = {
        id: course.id,
        name: course.name,
        logo: course.logo,
        community: course.community,
        link: course.link,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        organization: course.organization,
        modules: course.courseModules.map(cm => ({
          id: cm.module.id,
          name: cm.module.name,
          position: cm.module.position,
          cover: cm.module.cover,
          courseId: course.id,
          lessons: cm.module.lessons.map(lesson => ({
            id: lesson.id,
            name: lesson.name,
            description: lesson.description,
            videoUrl: lesson.videoUrl,
            position: lesson.position,
            moduleId: cm.module.id, // Use the module ID from the course module relationship
            thumbnail: lesson.thumbnail,
            duration: lesson.duration,
            externalLink: lesson.externalLink,
            userWatchedLessons: lesson.userWatchedLessons[0] ? {
              isCompleted: lesson.userWatchedLessons[0].isCompleted,
              currentTime: lesson.userWatchedLessons[0].currentTime,
              duration: lesson.userWatchedLessons[0].duration,
            } : undefined,
          }))
        }))
      }

      console.log('✅ Course API - Course data formatted successfully')
      return c.json({ course: formattedCourse })
    } catch (error) {
      console.error('❌ Course API - Error fetching course:', error)
      return c.json({ error: 'Failed to fetch course' }, 500)
    }
  })
