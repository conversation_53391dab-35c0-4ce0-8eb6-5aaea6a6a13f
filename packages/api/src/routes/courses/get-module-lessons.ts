import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  moduleId: z.string(),
})

export const getModuleLessons = new Hono()
	.use(authMiddleware)
	.get('/module/:moduleId/lessons', validator('param', paramsSchema),
		describeRoute({
			summary: "Get module lessons",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const { moduleId } = c.req.valid('param')

      // Replace with Prisma query
      const lessons = await db.lessons.findMany({
        where: { moduleId },
        orderBy: { position: 'asc' }
      });

      const formattedLessons = lessons.map(lesson => ({
        id: lesson.id,
        name: lesson.name,
        description: lesson.description,
        duration: lesson.duration,
        videoUrl: lesson.videoUrl,
        moduleId: lesson.moduleId,
        position: lesson.position,
        // isCompleted is user-specific, set to false or handle separately
      }));

      return c.json({ lessons })
    } catch (error) {
      return c.json({ error: 'Failed to fetch module lessons' }, 500)
    }
  })
