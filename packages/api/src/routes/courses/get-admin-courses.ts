import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const querySchema = z.object({
	organizationSlug: z.string().optional(),
	status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).optional(),
	limit: z.coerce.number().min(1).max(100).default(50),
	offset: z.coerce.number().min(0).default(0),
});

export const getAdminCourses = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.get("/", validator("query", querySchema),
		describeRoute({
			summary: "Get admin courses",
			tags: ["Courses"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { organizationSlug, status, limit, offset } = c.req.valid("query");

			// Build where conditions
			const where: any = {};

			// Filter by organization if specified
			if (organizationSlug) {
				const organization = await db.organization.findFirst({
					where: { slug: organizationSlug },
					select: { id: true },
				});

				if (!organization) {
					return c.json({ error: "Organization not found" }, 404);
				}

				where.organizationId = organization.id;
			}

			// Get courses with comprehensive data
			const courses = await db.courses.findMany({
				where,
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					courseModules: {
						include: {
							module: {
								select: {
									id: true,
									name: true,
									position: true,
									cover: true,
								},
							},
						},
						orderBy: { module: { position: "asc" } },
					},
					userCourses: {
						select: {
							userId: true,
						},
					},
				},
				orderBy: { createdAt: "desc" },
				skip: offset,
				take: limit,
			});

			// Get total count for pagination
			const totalCount = await db.courses.count({ where });

			// Transform the data to match the expected format
			const transformedCourses = courses.map((course) => ({
				id: course.id,
				name: course.name,
				logo: course.logo,
				community: course.community,
				description: course.community || `Curso de ${course.name}`,
				studentsCount: course.userCourses.length,
				modulesCount: course.courseModules.length,
				status: "published", // Since we don't have status in schema, defaulting to published
				type: "free", // Since we don't have pricing in current schema, defaulting to free
				createdAt: course.createdAt.toISOString(),
				updatedAt: course.updatedAt.toISOString(),
				organizationId: course.organizationId,
				organization: course.organization,
				creator: course.creator,
				modules: course.courseModules.map((cm) => ({
					id: cm.module.id,
					name: cm.module.name,
					position: cm.module.position,
					cover: cm.module.cover,
				})),
			}));

			// Get organizations for filter dropdown
			const organizations = await db.organization.findMany({
				select: {
					id: true,
					name: true,
					slug: true,
					_count: {
						select: {
							courses: true,
						},
					},
				},
				orderBy: { name: "asc" },
			});

			return c.json({
				courses: transformedCourses,
				totalCount,
				organizations: organizations.map((org) => ({
					id: org.id,
					name: org.name,
					slug: org.slug,
					coursesCount: org._count.courses,
				})),
				pagination: {
					limit,
					offset,
					hasMore: offset + limit < totalCount,
				},
			});
		} catch (error) {
			console.error("Error fetching admin courses:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
