import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  lessonId: z.string(),
})

const bodySchema = z.object({
  progress: z.number().min(0).max(100),
  completed: z.boolean().optional(),
  watchTime: z.number().min(0).optional(),
})

export const watchLesson = new Hono()
	.use(authMiddleware)
	.post('/lesson/:lessonId/watch', validator('param', paramsSchema), validator('json', bodySchema),
		describeRoute({
			summary: "Mark lesson as watched",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const { lessonId } = c.req.valid('param')
      const body = c.req.valid('json')

      console.log(`Updating lesson ${lessonId} progress:`, body)

      return c.json({
        success: true,
        message: 'Lesson progress updated successfully',
        data: {
          lessonId,
          ...body,
          updatedAt: new Date().toISOString(),
        },
      })
    } catch (error) {
      return c.json({ error: 'Failed to update lesson progress' }, 500)
    }
  })
