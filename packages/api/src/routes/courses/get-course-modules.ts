import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  courseId: z.string(),
})

export const getCourseModules = new Hono()
	.use(authMiddleware)
	.get('/course/:courseId/modules', validator('param', paramsSchema),
		describeRoute({
			summary: "Get course modules",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const { courseId } = c.req.valid('param')

      // Replace the mock data with Prisma query
      const courseModules = await db.courseModules.findMany({
        where: { courseId },
        include: {
          module: {
            select: {
              id: true,
              name: true,
              position: true,
              lessons: {
                select: { id: true }
              }
            }
          }
        },
        orderBy: { module: { position: 'asc' } }
      });

      const modules = courseModules.map(cm => ({
        id: cm.module.id,
        name: cm.module.name,
        position: cm.module.position,
        lessonsCount: cm.module.lessons.length,
        // Add other fields as needed, calculate duration if necessary
      }));

      return c.json({ modules })
    } catch (error) {
      return c.json({ error: 'Failed to fetch course modules' }, 500)
    }
  })
