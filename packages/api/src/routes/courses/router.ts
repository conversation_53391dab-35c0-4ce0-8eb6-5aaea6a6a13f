import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { getCoursePreview } from "./get-course-preview";
import { getCourse } from "./get-course";

const coursesRouter = new Hono()
	.basePath("/courses")
	.use("*", authMiddleware);

coursesRouter.get("/test",
	describeRoute({
		summary: "Test course API",
		tags: ["Courses"],
	}),
	(c) => {
		return c.json({ message: "Course API is working!" });
	});

coursesRouter.get("/test/simple",
	describeRoute({
		summary: "Simple test route",
		tags: ["Courses"],
	}),
	(c) => {
		return c.json({ message: "Simple test route working!" });
	});

coursesRouter.get("/test/auth", authMiddleware,
	describeRoute({
		summary: "Auth test route",
		tags: ["Courses"],
	}),
	(c) => {
		return c.json({ message: "Auth test route working!", user: c.get("user") });
	});

// Removed duplicate course route - using the one from get-course.ts instead

// Add course routes
coursesRouter.route("/", getCourse);
coursesRouter.route("/", getCoursePreview);

// Removed catch-all to avoid conflicts with admin routes

export { coursesRouter };
