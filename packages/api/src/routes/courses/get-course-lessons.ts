import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  courseId: z.string(),
})

export const getCourseLessons = new Hono()
	.use(authMiddleware)
	.get('/course/:courseId/lessons', validator('param', paramsSchema),
		describeRoute({
			summary: "Get course lessons",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const { courseId } = c.req.valid('param')

      // Replace with Prisma query
      const lessons = await db.lessons.findMany({
        where: {
          module: {
            courseModules: {
              some: { courseId }
            }
          }
        },
        include: {
          module: {
            select: { id: true }
          }
        },
        orderBy: { position: 'asc' }
      });

      const formattedLessons = lessons.map(lesson => ({
        id: lesson.id,
        name: lesson.name,
        description: lesson.description,
        duration: lesson.duration,
        videoUrl: lesson.videoUrl,
        courseId,
        moduleId: lesson.module.id,
        position: lesson.position,
        // isCompleted would be user-specific, so omitted or set to false
      }));

      return c.json({ lessons })
    } catch (error) {
      return c.json({ error: 'Failed to fetch course lessons' }, 500)
    }
  })
