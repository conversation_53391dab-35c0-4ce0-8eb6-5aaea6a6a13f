import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const paramsSchema = z.object({
	courseId: z.string(),
});

export const deleteAdminCourse = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.delete("/:courseId", validator("param", paramsSchema),
		describeRoute({
			summary: "Delete admin course",
			tags: ["Courses"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { courseId } = c.req.valid("param");

			console.log('🔍 Admin Course Delete API - Starting course deletion:', { courseId, userId: user.id, userRole: user.role });

			// Check if course exists
			const course = await db.courses.findUnique({
				where: { id: courseId },
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
				},
			});

			if (!course) {
				console.log('❌ Admin Course Delete API - Course not found');
				return c.json({ error: "Course not found" }, 404);
			}

			console.log('✅ Admin Course Delete API - Course found:', { 
				courseId: course.id, 
				courseName: course.name,
				organizationId: course.organizationId 
			});

			// Delete related data first (due to foreign key constraints)
			
			// Delete user watched lessons
			await db.userWatchedLessons.deleteMany({
				where: {
					lesson: {
						module: {
							courseModules: {
								some: {
									courseId: courseId
								}
							}
						}
					}
				}
			});

			// Delete lesson comments and replies
			await db.lessonCommentReplies.deleteMany({
				where: {
					lessonComment: {
						lesson: {
							module: {
								courseModules: {
									some: {
										courseId: courseId
									}
								}
							}
						}
					}
				}
			});

			await db.lessonComments.deleteMany({
				where: {
					lesson: {
						module: {
							courseModules: {
								some: {
									courseId: courseId
								}
							}
						}
					}
				}
			});

			// Delete lesson files
			await db.lessonFiles.deleteMany({
				where: {
					lesson: {
						module: {
							courseModules: {
								some: {
									courseId: courseId
								}
							}
						}
					}
				}
			});

			// Delete user courses
			await db.userCourses.deleteMany({
				where: { courseId: courseId }
			});

			// Delete vitrine section courses
			await db.vitrineSectionCourse.deleteMany({
				where: { courseId: courseId }
			});

			// Delete course products
			await db.courseProduct.deleteMany({
				where: { courseId: courseId }
			});

			// Delete course banners
			await db.courseBanner.deleteMany({
				where: { courseId: courseId }
			});

			// Delete course banner buttons
			await db.courseBannerButton.deleteMany({
				where: { courseId: courseId }
			});

			// Get all modules for this course
			const courseModules = await db.courseModules.findMany({
				where: { courseId: courseId },
				include: {
					module: {
						include: {
							lessons: true
						}
					}
				}
			});

			// Delete lessons
			for (const courseModule of courseModules) {
				await db.lessons.deleteMany({
					where: { moduleId: courseModule.module.id }
				});
			}

			// Delete course modules
			await db.courseModules.deleteMany({
				where: { courseId: courseId }
			});

			// Delete modules
			for (const courseModule of courseModules) {
				await db.modules.delete({
					where: { id: courseModule.module.id }
				});
			}

			// Finally, delete the course
			await db.courses.delete({
				where: { id: courseId }
			});

			console.log('✅ Admin Course Delete API - Course deleted successfully');

			return c.json({ 
				success: true, 
				message: "Course deleted successfully",
				deletedCourse: {
					id: course.id,
					name: course.name,
					organizationId: course.organizationId
				}
			});

		} catch (error) {
			console.error("❌ Admin Course Delete API - Error deleting course:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
