import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const paramsSchema = z.object({
	courseId: z.string(),
});

export const getAdminCourse = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.get("/:courseId", validator("param", paramsSchema),
		describeRoute({
			summary: "Get admin course details",
			tags: ["Courses"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { courseId } = c.req.valid("param");

			console.log('🔍 Admin Course API - Starting course fetch:', { courseId, userId: user.id, userRole: user.role });

			// Fetch course with all related data for admin editing
			const course = await db.courses.findUnique({
				where: { id: courseId },
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
					courseModules: {
						include: {
							module: {
								include: {
									lessons: {
										orderBy: { position: 'asc' },
										select: {
											id: true,
											name: true,
											description: true,
											position: true,
											videoUrl: true,
											thumbnail: true,
											duration: true,
											externalLink: true,
											createdAt: true,
											updatedAt: true,
										},
									},
								},
							},
						},
						orderBy: { module: { position: 'asc' } },
					},
				},
			});

			console.log('🔍 Admin Course API - Course found:', course ? { id: course.id, name: course.name, organizationId: course.organizationId } : 'NOT FOUND');

			if (!course) {
				console.log('❌ Admin Course API - Course not found:', { courseId });
				return c.json({ error: 'Course not found' }, 404);
			}

			// Format the course data for admin editing
			const formattedCourse = {
				id: course.id,
				name: course.name,
				description: course.description,
				community: course.community,
				link: course.link,
				logo: course.logo,
				organizationId: course.organizationId,
				createdAt: course.createdAt.toISOString(),
				updatedAt: course.updatedAt.toISOString(),
				organization: course.organization,
				modules: course.courseModules.map((cm) => ({
					id: cm.module.id,
					name: cm.module.name,
					position: cm.module.position,
					cover: cm.module.cover,
					courseId: course.id,
					createdAt: cm.module.createdAt.toISOString(),
					updatedAt: cm.module.updatedAt.toISOString(),
					lessons: cm.module.lessons.map((lesson) => ({
						id: lesson.id,
						name: lesson.name,
						description: lesson.description,
						position: lesson.position,
						videoUrl: lesson.videoUrl,
						thumbnail: lesson.thumbnail,
						duration: lesson.duration,
						externalLink: lesson.externalLink,
						moduleId: cm.module.id,
						createdAt: lesson.createdAt.toISOString(),
						updatedAt: lesson.updatedAt.toISOString(),
					})),
				})),
			};

			console.log('✅ Admin Course API - Course data formatted successfully');
			return c.json(formattedCourse);
		} catch (error) {
			console.error('❌ Admin Course API - Error fetching course:', error);
			return c.json({ error: 'Failed to fetch course' }, 500);
		}
	});
