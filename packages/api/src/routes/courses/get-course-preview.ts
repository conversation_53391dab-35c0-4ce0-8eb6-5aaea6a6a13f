import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const querySchema = z.object({
	organizationSlug: z.string(),
});

export const getCoursePreview = new Hono()
	.use(authMiddleware)
	.get("/:courseId/preview", validator("query", querySchema),
		describeRoute({
			summary: "Get course preview for modal",
			tags: ["Courses"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const courseId = c.req.param("courseId");
			const { organizationSlug } = c.req.valid("query");

			if (!courseId || !organizationSlug) {
				return c.json({ error: "Course ID and organization slug are required" }, 400);
			}

			const organization = await db.organization.findFirst({
				where: { slug: organizationSlug },
			});

			if (!organization) {
				return c.json({ error: "Organization not found" }, 404);
			}

			// Buscar curso com módulos e aulas
			const course = await db.courses.findFirst({
				where: {
					id: courseId,
					organizationId: organization.id,
				},
				include: {
					courseModules: {
						include: {
							module: {
								include: {
									lessons: {
										orderBy: { position: "asc" },
										include: {
											userWatchedLessons: {
												where: { userId: user.id },
											},
										},
									},
								},
							},
						},
						orderBy: { module: { position: "asc" } },
					},
				},
			});

			if (!course) {
				return c.json({ error: "Course not found" }, 404);
			}

			// Verificar se usuário tem acesso ao curso
			const userCourseAccess = await db.userCourses.findFirst({
				where: {
					userId: user.id,
					courseId: course.id,
				},
			});

			if (!userCourseAccess && course.createdBy !== user.id) {
				return c.json({ error: "User does not have access to this course" }, 403);
			}

			// Formatar dados para o modal
			const modules = course.courseModules.map((courseModule) => ({
				id: courseModule.module.id,
				title: courseModule.module.name,
				lessons: courseModule.module.lessons.map((lesson) => ({
					id: lesson.id,
					title: lesson.name,
					duration: lesson.duration || "0 min",
					thumbnail: lesson.thumbnail,
					isCompleted: lesson.userWatchedLessons?.[0]?.isCompleted || false,
					videoUrl: lesson.videoUrl,
				})),
			}));

			// Primeira aula (se existir)
			const firstLesson = modules[0]?.lessons[0];

			const response = {
				id: course.id,
				title: course.name,
				description: course.description,
				logo: course.logo,
				modules,
				firstLesson,
			};

			return c.json(response);
		} catch (error) {
			console.error("Error fetching course preview:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
