import { countAllUsers, getUsers, createUser, createUserAccount, getUserByEmail } from "@repo/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";
import { auth } from "@repo/auth";
import { sendEmail } from "@repo/mail";
import { getBaseUrl } from "@repo/utils";

const createUserSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório"),
	email: z.string().email("Email inválido"),
	role: z.enum(["user", "admin"]).default("user"),
	sendEmail: z.boolean().default(true),
});

export const userRouter = new Hono()
	.basePath("/users")
	.use(adminMiddleware)
	.get(
		"/",
		validator(
			"query",
			z.object({
				query: z.string().optional(),
				limit: z.string().optional().default("10").transform(Number),
				offset: z.string().optional().default("0").transform(Number),
			}),
		),
		describeRoute({
			summary: "Get all users",
			tags: ["Administration"],
		}),
		async (c) => {
			const { query, limit, offset } = c.req.valid("query");

			const users = await getUsers({
				limit,
				offset,
				query,
			});

			const total = await countAllUsers();

			return c.json({ users, total });
		},
	)
	.post(
		"/",
		validator("json", createUserSchema),
		describeRoute({
			summary: "Create a new user",
			tags: ["Administration"],
		}),
		async (c) => {
			try {
				const { name, email, role, sendEmail: shouldSendEmail } = c.req.valid("json");

				// Check if user already exists
				const existingUser = await getUserByEmail(email);
				if (existingUser) {
					return c.json({ error: "Usuário com este email já existe" }, 400);
				}

				// Generate random password
				const authContext = await auth.$context;
				const password = Math.random().toString(36).slice(-12) + Math.random().toString(36).toUpperCase().slice(-4);
				const hashedPassword = await authContext.password.hash(password);

				// Create user using database functions (Better Auth pattern)
				const user = await createUser({
					email,
					name,
					role,
					emailVerified: true,
					onboardingComplete: false,
				});

				if (!user) {
					return c.json({ error: "Falha ao criar usuário" }, 500);
				}

				// Create user account with password
				await createUserAccount({
					userId: user.id,
					providerId: "credential",
					accountId: user.id,
					hashedPassword,
				});

				// Send welcome email if requested
				if (shouldSendEmail) {
					const loginUrl = `${getBaseUrl()}/auth/login`;

					await sendEmail({
						to: email,
						templateId: "userCreated",
						context: {
							name,
							email,
							password,
							url: loginUrl,
						},
						locale: "pt",
					});
				}

				return c.json({
					success: true,
					user: {
						id: user.id,
						email: user.email,
						name: user.name,
						role: user.role,
					},
					credentials: shouldSendEmail ? { email, password } : null,
				});

			} catch (error) {
				console.error("Error creating user:", error);
				return c.json({ error: "Erro interno do servidor" }, 500);
			}
		},
	);
