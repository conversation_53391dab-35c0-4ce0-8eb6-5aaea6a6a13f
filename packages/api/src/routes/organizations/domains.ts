import { Hono } from "hono";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { db } from "@repo/database";

const updateDomainSchema = z.object({
  organizationId: z.string(),
  subdomain: z.string().optional(),
  customDomain: z.string().optional(),
});

export const domainsRouter = new Hono()
  .use(authMiddleware)
  .patch("/domains", validator("json", updateDomainSchema),
		describeRoute({
			summary: "Update organization domains",
			tags: ["Organizations"],
		}),
		async (c) => {
    const { organizationId, subdomain, customDomain } = c.req.valid("json");

    const organization = await db.organization.update({
      where: { id: organizationId },
      data: {
        subdomain,
        customDomain,
      },
    });

    return c.json(organization);
  });
