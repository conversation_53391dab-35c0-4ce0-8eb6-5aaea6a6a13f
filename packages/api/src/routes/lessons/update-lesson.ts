import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const updateLessonSchema = z.object({
	name: z.string().min(1, "Lesson name is required").optional(),
	description: z.string().optional(),
	videoUrl: z.string().optional(),
	position: z.number().int().min(0).optional(),
	thumbnail: z.string().optional(),
	duration: z.string().optional(),
	externalLink: z.string().optional(),
});

const lessonParamsSchema = z.object({
	lessonId: z.string(),
});

export const updateLesson = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.put("/:lessonId", validator("param", lessonParamsSchema), validator("json", updateLessonSchema),
		describeRoute({
			summary: "Update a lesson",
			tags: ["Lessons"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { lessonId } = c.req.valid("param");
			const lessonData = c.req.valid("json");

			// Verify lesson exists and user has access
			const lesson = await db.lessons.findUnique({
				where: { id: lessonId },
				include: {
					module: {
						include: {
							courseModules: {
								include: {
									course: {
										select: {
											organizationId: true,
										},
									},
								},
							},
						},
					},
				},
			});

			if (!lesson) {
				return c.json({ error: "Lesson not found" }, 404);
			}

			// Check user permission in organization
			const organizationId = lesson.module.courseModules[0]?.course.organizationId;
			if (!organizationId) {
				return c.json({ error: "Course organization not found" }, 404);
			}

			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can update lessons" }, 403);
			}

			// Update lesson
			const updatedLesson = await db.lessons.update({
				where: { id: lessonId },
				data: lessonData,
			});

			return c.json({ lesson: updatedLesson });
		} catch (error) {
			console.error("Error updating lesson:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
