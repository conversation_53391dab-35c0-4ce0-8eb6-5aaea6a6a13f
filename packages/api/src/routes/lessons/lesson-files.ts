import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const createLessonFileSchema = z.object({
	lessonId: z.string(),
	name: z.string().min(1, "File name is required"),
	url: z.string().url("Valid URL is required"),
	type: z.enum(['pdf', 'document', 'image', 'video', 'audio', 'other']),
	size: z.number().int().min(0, "File size must be a positive number"),
});

const updateLessonFileSchema = z.object({
	name: z.string().min(1, "File name is required").optional(),
	url: z.string().url("Valid URL is required").optional(),
	type: z.enum(['pdf', 'document', 'image', 'video', 'audio', 'other']).optional(),
	size: z.number().int().min(0, "File size must be a positive number").optional(),
});

const lessonFileParamsSchema = z.object({
	fileId: z.string(),
});

const lessonParamsSchema = z.object({
	lessonId: z.string(),
});

export const lessonFiles = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	// Get all files for a lesson
	.get("/:lessonId/files", validator("param", lessonParamsSchema),
		describeRoute({
			summary: "Get lesson files",
			tags: ["Lessons"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { lessonId } = c.req.valid("param");

			// Verify lesson exists and user has access
			const lesson = await db.lessons.findUnique({
				where: { id: lessonId },
				include: {
					module: {
						include: {
							courseModules: {
								include: {
									course: {
										select: {
											organizationId: true,
										},
									},
								},
							},
						},
					},
				},
			});

			if (!lesson) {
				return c.json({ error: "Lesson not found" }, 404);
			}

			// Check user permission in organization
			const organizationId = lesson.module.courseModules[0]?.course.organizationId;
			if (!organizationId) {
				return c.json({ error: "Course organization not found" }, 404);
			}

			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can access lesson files" }, 403);
			}

			// Get lesson files
			const files = await db.lessonFiles.findMany({
				where: { lessonId },
				orderBy: { createdAt: "asc" },
			});

			return c.json({ files });
		} catch (error) {
			console.error("Error fetching lesson files:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	})
	// Create a new lesson file
	.post("/:lessonId/files", validator("param", lessonParamsSchema), validator("json", createLessonFileSchema),
		describeRoute({
			summary: "Create lesson file",
			tags: ["Lessons"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { lessonId } = c.req.valid("param");
			const fileData = c.req.valid("json");

			// Verify lesson exists and user has access
			const lesson = await db.lessons.findUnique({
				where: { id: lessonId },
				include: {
					module: {
						include: {
							courseModules: {
								include: {
									course: {
										select: {
											organizationId: true,
										},
									},
								},
							},
						},
					},
				},
			});

			if (!lesson) {
				return c.json({ error: "Lesson not found" }, 404);
			}

			// Check user permission in organization
			const organizationId = lesson.module.courseModules[0]?.course.organizationId;
			if (!organizationId) {
				return c.json({ error: "Course organization not found" }, 404);
			}

			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can create lesson files" }, 403);
			}

			// Create lesson file
			const file = await db.lessonFiles.create({
				data: {
					lessonId,
					name: fileData.name,
					url: fileData.url,
					type: fileData.type,
					size: fileData.size,
				},
			});

			return c.json({ file }, 201);
		} catch (error) {
			console.error("Error creating lesson file:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	})
	// Update a lesson file
	.put("/files/:fileId", validator("param", lessonFileParamsSchema), validator("json", updateLessonFileSchema),
		describeRoute({
			summary: "Update lesson file",
			tags: ["Lessons"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { fileId } = c.req.valid("param");
			const updateData = c.req.valid("json");

			// Verify file exists and user has access
			const file = await db.lessonFiles.findUnique({
				where: { id: parseInt(fileId) },
				include: {
					lesson: {
						include: {
							module: {
								include: {
									courseModules: {
										include: {
											course: {
												select: {
													organizationId: true,
												},
											},
										},
									},
								},
							},
						},
					},
				},
			});

			if (!file) {
				return c.json({ error: "File not found" }, 404);
			}

			// Check user permission in organization
			const organizationId = file.lesson.module.courseModules[0]?.course.organizationId;
			if (!organizationId) {
				return c.json({ error: "Course organization not found" }, 404);
			}

			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can update lesson files" }, 403);
			}

			// Update lesson file
			const updatedFile = await db.lessonFiles.update({
				where: { id: parseInt(fileId) },
				data: updateData,
			});

			return c.json({ file: updatedFile });
		} catch (error) {
			console.error("Error updating lesson file:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	})
	// Delete a lesson file
	.delete("/files/:fileId", validator("param", lessonFileParamsSchema),
		describeRoute({
			summary: "Delete lesson file",
			tags: ["Lessons"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { fileId } = c.req.valid("param");

			// Verify file exists and user has access
			const file = await db.lessonFiles.findUnique({
				where: { id: parseInt(fileId) },
				include: {
					lesson: {
						include: {
							module: {
								include: {
									courseModules: {
										include: {
											course: {
												select: {
													organizationId: true,
												},
											},
										},
									},
								},
							},
						},
					},
				},
			});

			if (!file) {
				return c.json({ error: "File not found" }, 404);
			}

			// Check user permission in organization
			const organizationId = file.lesson.module.courseModules[0]?.course.organizationId;
			if (!organizationId) {
				return c.json({ error: "Course organization not found" }, 404);
			}

			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can delete lesson files" }, 403);
			}

			// Delete lesson file
			await db.lessonFiles.delete({
				where: { id: parseInt(fileId) },
			});

			return c.json({ message: "File deleted successfully" });
		} catch (error) {
			console.error("Error deleting lesson file:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
