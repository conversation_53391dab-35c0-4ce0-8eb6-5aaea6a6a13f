import { cors } from "hono/cors";
import { getBaseUrl } from "@repo/utils";

export const corsMiddleware = cors({
	origin: [getBaseUrl(), "http://localhost:3000", "http://localhost:3001"],
	allowHeaders: [
		"Content-Type",
		"Authorization",
		"Cookie",
		"Set-Cookie",
		"X-Requested-With"
	],
	allowMethods: ["POST", "GET", "PUT", "DELETE", "OPTIONS", "PATCH"],
	exposeHeaders: ["Content-Length", "Set-Cookie"],
	maxAge: 600,
	credentials: true, // ✅ Critical for cookie handling
});
