import { logger } from "@repo/logs";
import { logger as honoLogger } from "hono/logger";

export const loggerMiddleware = honoLogger((message, ...rest) => {
	// Ensure logging works in production
	if (process.env.NODE_ENV === "production") {
		logger.info(`[API] ${message}`, ...rest);
	} else {
		logger.log(message, ...rest);
	}
});

// Add a startup log
logger.info(`[API] Starting API server in ${process.env.NODE_ENV} mode`);
