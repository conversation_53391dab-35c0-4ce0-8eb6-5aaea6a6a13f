import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface UserCreatedProps {
  name?: string;
  email: string;
  password?: string;
  url: string;
}

export const UserCreated = ({
  name,
  email,
  password,
  url
}: UserCreatedProps) => (
  <Wrapper>
    <Preview>Bem-vindo ao Cakto Members! Sua conta foi criada com sucesso</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 24, margin: "24px 0 8px", color: "#0F7864" }}>
        🎉 Bem-vindo ao Cakto Members!
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0", lineHeight: "1.6" }}>
        Olá{ name ? ` ${name}` : "" },<br />
        Sua conta no Cakto Members foi criada com sucesso!<br />
        Agora você tem acesso a uma plataforma completa de cursos, conteúdos exclusivos e uma comunidade ativa de membros.
      </Text>

      <Section style={{
        background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
        padding: "20px",
        borderRadius: "8px",
        margin: "24px 0",
        textAlign: "left",
        border: "1px solid #dee2e6"
      }}>
        <Text style={{ margin: "0 0 12px 0", fontSize: 16, fontWeight: "600", color: "#0F7864" }}>
          🔐 Suas credenciais de acesso:
        </Text>
        <Text style={{ margin: "8px 0", fontSize: 14, color: "#495057" }}>
          <strong>Email:</strong> {email}
        </Text>
        {password && (
          <Text style={{ margin: "8px 0", fontSize: 14, color: "#495057" }}>
            <strong>Senha:</strong> {password}
          </Text>
        )}
      </Section>

      <Section style={{ margin: "32px 0" }}>
        <Link
          href={url}
          style={{
            display: "inline-block",
            background: "linear-gradient(135deg, #0F7864 0%, #0d6b5a 100%)",
            color: "#fff",
            padding: "14px 36px",
            borderRadius: "8px",
            fontWeight: "600",
            textDecoration: "none",
            fontSize: 16,
            boxShadow: "0 4px 12px rgba(15, 120, 100, 0.3)",
            transition: "all 0.3s ease"
          }}
        >
          🚀 Acessar Minha Conta
        </Link>
      </Section>

      <Section style={{
        background: "#fff3cd",
        padding: "16px",
        borderRadius: "8px",
        margin: "24px 0",
        border: "1px solid #ffeaa7"
      }}>
        <Text style={{ color: "#856404", fontSize: 14, margin: "0", textAlign: "left" }}>
          <strong>⚠️ Importante:</strong> Por segurança, recomendamos que você altere sua senha no primeiro acesso através das configurações da sua conta.
        </Text>
      </Section>

      <Section style={{ margin: "32px 0", textAlign: "left" }}>
        <Text style={{ fontSize: 16, fontWeight: "600", color: "#0F7864", margin: "0 0 12px 0" }}>
          🎯 O que você encontrará na plataforma:
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Cursos exclusivos e conteúdos premium
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Comunidade ativa de membros
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Suporte direto da equipe
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Atualizações e novidades em primeira mão
        </Text>
      </Section>

      <Text style={{ color: "#6c757d", fontSize: 14, margin: "24px 0 0", fontStyle: "italic" }}>
        Se você tiver alguma dúvida ou precisar de suporte, nossa equipe está aqui para ajudar!
      </Text>
    </Section>
  </Wrapper>
);

export default UserCreated;
