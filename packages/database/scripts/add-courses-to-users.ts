import { PrismaClient } from "@prisma/client";
import { config } from "dotenv";
import { resolve } from "path";

// Carregar variáveis de ambiente
config({ path: resolve(__dirname, "../../../.env") });

const prisma = new PrismaClient();

async function main() {
  console.log("🎓 Adicionando todos os cursos aos usuários admin...");

  // Buscar os usuários
  const ulisses = await prisma.user.findUnique({
    where: { email: "<EMAIL>" }
  });

  const admin = await prisma.user.findUnique({
    where: { email: "<EMAIL>" }
  });

  if (!ulisses) {
    console.log("❌ Usuário <EMAIL> não encontrado");
    return;
  }

  if (!admin) {
    console.log("❌ Usuário <EMAIL> não encontrado");
    return;
  }

  // Buscar todos os cursos
  const courses = await prisma.courses.findMany();

  if (courses.length === 0) {
    console.log("❌ Nenhum curso encontrado");
    return;
  }

  console.log(`📚 Encontrados ${courses.length} cursos`);

  // Adicionar todos os cursos para Ulisses
  console.log("👤 Adicionando cursos para Ulisses...");
  const ulissesCourses = await Promise.all(
    courses.map(async (course) => {
      return prisma.userCourses.upsert({
        where: {
          userId_courseId: {
            userId: ulisses.id,
            courseId: course.id
          }
        },
        update: {},
        create: {
          userId: ulisses.id,
          courseId: course.id
        }
      });
    })
  );

  console.log(`✅ ${ulissesCourses.length} cursos adicionados para Ulisses`);

  // Adicionar todos os cursos para Admin
  console.log("👤 Adicionando cursos para Admin...");
  const adminCourses = await Promise.all(
    courses.map(async (course) => {
      return prisma.userCourses.upsert({
        where: {
          userId_courseId: {
            userId: admin.id,
            courseId: course.id
          }
        },
        update: {},
        create: {
          userId: admin.id,
          courseId: course.id
        }
      });
    })
  );

  console.log(`✅ ${adminCourses.length} cursos adicionados para Admin`);

  console.log("\n🎉 Processo concluído!");
  console.log(`📊 Total de cursos: ${courses.length}`);
  console.log(`👤 Cursos para Ulisses: ${ulissesCourses.length}`);
  console.log(`👤 Cursos para Admin: ${adminCourses.length}`);
}

main()
  .catch(e => {
    console.error('❌ Erro:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
